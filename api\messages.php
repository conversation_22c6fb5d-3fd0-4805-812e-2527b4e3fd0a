<?php
/**
 * Messages API for Mobile App
 * Handles contact form messages and communication
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

require_once __DIR__ . '/../includes/database.php';
require_once __DIR__ . '/../includes/functions.php';

$method = $_SERVER['REQUEST_METHOD'];

// Authenticate user for admin operations
if (in_array($method, ['GET', 'PUT', 'DELETE']) || 
    ($method === 'POST' && isset($_GET['action']) && $_GET['action'] !== 'contact')) {
    $authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? '';
    $tokenData = validateApiToken($authHeader);
    
    if (!$tokenData) {
        sendErrorResponse('Unauthorized', 401);
    }
}

try {
    switch ($method) {
        case 'GET':
            handleGetMessages();
            break;
            
        case 'POST':
            if (isset($_GET['action']) && $_GET['action'] === 'contact') {
                handleContactForm();
            } else {
                sendErrorResponse('Invalid action', 400);
            }
            break;
            
        case 'PUT':
            handleUpdateMessage();
            break;
            
        case 'DELETE':
            handleDeleteMessage();
            break;
            
        default:
            sendErrorResponse('Method not allowed', 405);
    }
} catch (Exception $e) {
    error_log("Messages API Error: " . $e->getMessage());
    sendErrorResponse('Internal server error', 500);
}

/**
 * Get messages with optional filtering and pagination
 */
function handleGetMessages() {
    $page = intval($_GET['page'] ?? 1);
    $limit = intval($_GET['limit'] ?? 20);
    $status = $_GET['status'] ?? '';
    $search = $_GET['search'] ?? '';
    $messageId = $_GET['id'] ?? '';
    
    // If specific message ID is requested
    if (!empty($messageId)) {
        $message = fetchOne("SELECT * FROM messages WHERE id = ?", [$messageId]);
        
        if (!$message) {
            sendErrorResponse('Message not found', 404);
        }
        
        // Mark as read if it's new
        if ($message['status'] === 'new') {
            updateData('messages', ['status' => 'read'], 'id = ?', [$messageId]);
            $message['status'] = 'read';
        }
        
        sendSuccessResponse($message);
    }
    
    // Build query conditions
    $conditions = [];
    $params = [];
    
    if (!empty($status)) {
        $conditions[] = "status = ?";
        $params[] = $status;
    }
    
    if (!empty($search)) {
        $conditions[] = "(name LIKE ? OR email LIKE ? OR subject LIKE ? OR message LIKE ?)";
        $searchTerm = "%{$search}%";
        $params[] = $searchTerm;
        $params[] = $searchTerm;
        $params[] = $searchTerm;
        $params[] = $searchTerm;
    }
    
    $whereClause = !empty($conditions) ? 'WHERE ' . implode(' AND ', $conditions) : '';
    
    $sql = "SELECT m.*, u.username as replied_by_name
            FROM messages m
            LEFT JOIN users u ON m.replied_by = u.id
            {$whereClause}
            ORDER BY m.created_at DESC";
    
    $result = paginate($sql, $params, $page, $limit);
    
    sendSuccessResponse($result);
}

/**
 * Handle contact form submission (public endpoint)
 */
function handleContactForm() {
    $input = json_decode(file_get_contents('php://input'), true);
    
    // Validate required fields
    $required = ['name', 'email', 'message'];
    foreach ($required as $field) {
        if (empty($input[$field])) {
            sendErrorResponse("Field '{$field}' is required", 400);
        }
    }
    
    // Validate email
    if (!isValidEmail($input['email'])) {
        sendErrorResponse('Invalid email format', 400);
    }
    
    // Rate limiting - check for spam
    $ip = $_SERVER['REMOTE_ADDR'];
    $recentMessages = fetchOne(
        "SELECT COUNT(*) as count FROM messages 
         WHERE ip_address = ? AND created_at > DATE_SUB(NOW(), INTERVAL 1 HOUR)",
        [$ip]
    );
    
    if ($recentMessages && $recentMessages['count'] >= 5) {
        sendErrorResponse('Too many messages sent. Please try again later.', 429);
    }
    
    // Sanitize input
    $data = [
        'name' => sanitizeInput($input['name']),
        'email' => sanitizeInput($input['email']),
        'phone' => sanitizeInput($input['phone'] ?? ''),
        'subject' => sanitizeInput($input['subject'] ?? 'Website Contact Form'),
        'message' => sanitizeInput($input['message']),
        'ip_address' => $ip,
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
    ];
    
    try {
        $messageId = insertData('messages', $data);
        
        // Send notification email to admin (optional)
        sendAdminNotification($data);
        
        sendSuccessResponse(['id' => $messageId], 'Message sent successfully');
        
    } catch (Exception $e) {
        error_log("Contact form error: " . $e->getMessage());
        sendErrorResponse('Failed to send message', 500);
    }
}

/**
 * Update message status
 */
function handleUpdateMessage() {
    global $tokenData;
    
    $messageId = $_GET['id'] ?? '';
    
    if (empty($messageId)) {
        sendErrorResponse('Message ID is required', 400);
    }
    
    // Check if message exists
    $existingMessage = fetchOne("SELECT * FROM messages WHERE id = ?", [$messageId]);
    if (!$existingMessage) {
        sendErrorResponse('Message not found', 404);
    }
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    // Build update data
    $data = [];
    $allowedFields = ['status'];
    
    foreach ($allowedFields as $field) {
        if (isset($input[$field])) {
            if ($field === 'status') {
                $allowedStatuses = ['new', 'read', 'replied', 'archived'];
                if (in_array($input[$field], $allowedStatuses)) {
                    $data[$field] = $input[$field];
                    
                    // Set replied_at and replied_by if status is 'replied'
                    if ($input[$field] === 'replied') {
                        $data['replied_at'] = date('Y-m-d H:i:s');
                        $data['replied_by'] = $tokenData['user_id'];
                    }
                }
            }
        }
    }
    
    if (empty($data)) {
        sendErrorResponse('No valid fields to update', 400);
    }
    
    try {
        updateData('messages', $data, 'id = ?', [$messageId]);
        sendSuccessResponse([], 'Message updated successfully');
        
    } catch (Exception $e) {
        error_log("Update message error: " . $e->getMessage());
        sendErrorResponse('Failed to update message', 500);
    }
}

/**
 * Delete message
 */
function handleDeleteMessage() {
    $messageId = $_GET['id'] ?? '';
    
    if (empty($messageId)) {
        sendErrorResponse('Message ID is required', 400);
    }
    
    // Check if message exists
    $existingMessage = fetchOne("SELECT * FROM messages WHERE id = ?", [$messageId]);
    if (!$existingMessage) {
        sendErrorResponse('Message not found', 404);
    }
    
    try {
        deleteData('messages', 'id = ?', [$messageId]);
        sendSuccessResponse([], 'Message deleted successfully');
        
    } catch (Exception $e) {
        error_log("Delete message error: " . $e->getMessage());
        sendErrorResponse('Failed to delete message', 500);
    }
}

/**
 * Send admin notification email
 */
function sendAdminNotification($messageData) {
    try {
        $adminEmail = getSetting('company_email', '<EMAIL>');
        $subject = 'New Contact Form Message - ' . getSetting('company_name');
        
        $emailBody = "
        New message received from the website contact form:
        
        Name: {$messageData['name']}
        Email: {$messageData['email']}
        Phone: {$messageData['phone']}
        Subject: {$messageData['subject']}
        
        Message:
        {$messageData['message']}
        
        Sent from IP: {$messageData['ip_address']}
        Time: " . date('Y-m-d H:i:s') . "
        ";
        
        $headers = [
            'From: ' . getSetting('company_email'),
            'Reply-To: ' . $messageData['email'],
            'Content-Type: text/plain; charset=UTF-8'
        ];
        
        mail($adminEmail, $subject, $emailBody, implode("\r\n", $headers));
        
    } catch (Exception $e) {
        error_log("Failed to send admin notification: " . $e->getMessage());
    }
}

/**
 * Bulk update message status
 */
if ($_SERVER['REQUEST_METHOD'] === 'PUT' && isset($_GET['action']) && $_GET['action'] === 'bulk-update') {
    $authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? '';
    $tokenData = validateApiToken($authHeader);
    
    if (!$tokenData) {
        sendErrorResponse('Unauthorized', 401);
    }
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (empty($input['ids']) || !is_array($input['ids']) || empty($input['status'])) {
        sendErrorResponse('Message IDs array and status are required', 400);
    }
    
    $allowedStatuses = ['new', 'read', 'replied', 'archived'];
    if (!in_array($input['status'], $allowedStatuses)) {
        sendErrorResponse('Invalid status', 400);
    }
    
    $updatedCount = 0;
    
    try {
        foreach ($input['ids'] as $messageId) {
            $data = ['status' => $input['status']];
            
            // Set replied_at and replied_by if status is 'replied'
            if ($input['status'] === 'replied') {
                $data['replied_at'] = date('Y-m-d H:i:s');
                $data['replied_by'] = $tokenData['user_id'];
            }
            
            $updated = updateData('messages', $data, 'id = ?', [$messageId]);
            if ($updated > 0) {
                $updatedCount++;
            }
        }
        
        sendSuccessResponse(['updated_count' => $updatedCount], "{$updatedCount} messages updated successfully");
        
    } catch (Exception $e) {
        error_log("Bulk update messages error: " . $e->getMessage());
        sendErrorResponse('Failed to update messages', 500);
    }
}

/**
 * Get message statistics
 */
if ($_SERVER['REQUEST_METHOD'] === 'GET' && isset($_GET['action']) && $_GET['action'] === 'stats') {
    $authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? '';
    $tokenData = validateApiToken($authHeader);
    
    if (!$tokenData) {
        sendErrorResponse('Unauthorized', 401);
    }
    
    try {
        $stats = [
            'total_messages' => fetchOne("SELECT COUNT(*) as count FROM messages")['count'],
            'new_messages' => fetchOne("SELECT COUNT(*) as count FROM messages WHERE status = 'new'")['count'],
            'read_messages' => fetchOne("SELECT COUNT(*) as count FROM messages WHERE status = 'read'")['count'],
            'replied_messages' => fetchOne("SELECT COUNT(*) as count FROM messages WHERE status = 'replied'")['count'],
            'archived_messages' => fetchOne("SELECT COUNT(*) as count FROM messages WHERE status = 'archived'")['count'],
            'messages_today' => fetchOne("SELECT COUNT(*) as count FROM messages WHERE DATE(created_at) = CURDATE()")['count'],
            'messages_this_week' => fetchOne("SELECT COUNT(*) as count FROM messages WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)")['count'],
            'messages_this_month' => fetchOne("SELECT COUNT(*) as count FROM messages WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)")['count'],
            'recent_messages' => fetchAll("SELECT id, name, email, subject, status, created_at FROM messages ORDER BY created_at DESC LIMIT 10")
        ];
        
        sendSuccessResponse($stats);
        
    } catch (Exception $e) {
        error_log("Message stats error: " . $e->getMessage());
        sendErrorResponse('Failed to get message statistics', 500);
    }
}
?>

<?php
/**
 * Projects API for Mobile App
 * Handles CRUD operations for projects
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

require_once __DIR__ . '/../includes/database.php';
require_once __DIR__ . '/../includes/functions.php';

// Authenticate user for write operations
$method = $_SERVER['REQUEST_METHOD'];
if (in_array($method, ['POST', 'PUT', 'DELETE'])) {
    $authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? '';
    $tokenData = validateApiToken($authHeader);
    
    if (!$tokenData) {
        sendErrorResponse('Unauthorized', 401);
    }
}

try {
    switch ($method) {
        case 'GET':
            handleGetProjects();
            break;
            
        case 'POST':
            handleCreateProject();
            break;
            
        case 'PUT':
            handleUpdateProject();
            break;
            
        case 'DELETE':
            handleDeleteProject();
            break;
            
        default:
            sendErrorResponse('Method not allowed', 405);
    }
} catch (Exception $e) {
    error_log("Projects API Error: " . $e->getMessage());
    sendErrorResponse('Internal server error', 500);
}

/**
 * Get projects with optional filtering and pagination
 */
function handleGetProjects() {
    $page = intval($_GET['page'] ?? 1);
    $limit = intval($_GET['limit'] ?? 10);
    $category = $_GET['category'] ?? '';
    $featured = $_GET['featured'] ?? '';
    $search = $_GET['search'] ?? '';
    $projectId = $_GET['id'] ?? '';
    
    // If specific project ID is requested
    if (!empty($projectId)) {
        $project = fetchOne(
            "SELECT p.*, 
                    GROUP_CONCAT(pi.image_path) as additional_images
             FROM projects p
             LEFT JOIN project_images pi ON p.id = pi.project_id
             WHERE p.id = ?
             GROUP BY p.id",
            [$projectId]
        );
        
        if (!$project) {
            sendErrorResponse('Project not found', 404);
        }
        
        // Convert additional_images to array
        $project['additional_images'] = $project['additional_images'] ? 
            explode(',', $project['additional_images']) : [];
        
        sendSuccessResponse($project);
    }
    
    // Build query conditions
    $conditions = [];
    $params = [];
    
    if (!empty($category)) {
        $conditions[] = "category = ?";
        $params[] = $category;
    }
    
    if (!empty($featured)) {
        $conditions[] = "featured = ?";
        $params[] = $featured === 'true' ? 1 : 0;
    }
    
    if (!empty($search)) {
        $conditions[] = "(title LIKE ? OR description LIKE ? OR project_type LIKE ?)";
        $searchTerm = "%{$search}%";
        $params[] = $searchTerm;
        $params[] = $searchTerm;
        $params[] = $searchTerm;
    }
    
    $whereClause = !empty($conditions) ? 'WHERE ' . implode(' AND ', $conditions) : '';
    
    $sql = "SELECT p.*, 
                   (SELECT COUNT(*) FROM project_images pi WHERE pi.project_id = p.id) as image_count
            FROM projects p 
            {$whereClause}
            ORDER BY p.created_at DESC";
    
    $result = paginate($sql, $params, $page, $limit);
    
    sendSuccessResponse($result);
}

/**
 * Create new project
 */
function handleCreateProject() {
    $input = json_decode(file_get_contents('php://input'), true);
    
    // Validate required fields
    $required = ['title', 'description'];
    foreach ($required as $field) {
        if (empty($input[$field])) {
            sendErrorResponse("Field '{$field}' is required", 400);
        }
    }
    
    // Sanitize input
    $data = [
        'title' => sanitizeInput($input['title']),
        'description' => sanitizeInput($input['description']),
        'short_description' => sanitizeInput($input['short_description'] ?? ''),
        'category' => sanitizeInput($input['category'] ?? 'completed'),
        'project_type' => sanitizeInput($input['project_type'] ?? ''),
        'location' => sanitizeInput($input['location'] ?? ''),
        'client_name' => sanitizeInput($input['client_name'] ?? ''),
        'start_date' => $input['start_date'] ?? null,
        'end_date' => $input['end_date'] ?? null,
        'featured' => isset($input['featured']) ? ($input['featured'] ? 1 : 0) : 0,
        'status' => sanitizeInput($input['status'] ?? 'completed'),
        'budget' => isset($input['budget']) ? floatval($input['budget']) : null,
        'image_main' => sanitizeInput($input['image_main'] ?? '')
    ];
    
    // Validate dates
    if ($data['start_date'] && !validateDate($data['start_date'])) {
        sendErrorResponse('Invalid start date format', 400);
    }
    
    if ($data['end_date'] && !validateDate($data['end_date'])) {
        sendErrorResponse('Invalid end date format', 400);
    }
    
    try {
        $projectId = insertData('projects', $data);
        
        // Handle additional images if provided
        if (!empty($input['additional_images']) && is_array($input['additional_images'])) {
            foreach ($input['additional_images'] as $index => $imagePath) {
                insertData('project_images', [
                    'project_id' => $projectId,
                    'image_path' => sanitizeInput($imagePath),
                    'sort_order' => $index
                ]);
            }
        }
        
        sendSuccessResponse(['id' => $projectId], 'Project created successfully');
        
    } catch (Exception $e) {
        error_log("Create project error: " . $e->getMessage());
        sendErrorResponse('Failed to create project', 500);
    }
}

/**
 * Update existing project
 */
function handleUpdateProject() {
    $projectId = $_GET['id'] ?? '';
    
    if (empty($projectId)) {
        sendErrorResponse('Project ID is required', 400);
    }
    
    // Check if project exists
    $existingProject = fetchOne("SELECT * FROM projects WHERE id = ?", [$projectId]);
    if (!$existingProject) {
        sendErrorResponse('Project not found', 404);
    }
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    // Build update data (only include provided fields)
    $data = [];
    $allowedFields = [
        'title', 'description', 'short_description', 'category', 'project_type',
        'location', 'client_name', 'start_date', 'end_date', 'featured',
        'status', 'budget', 'image_main'
    ];
    
    foreach ($allowedFields as $field) {
        if (isset($input[$field])) {
            if (in_array($field, ['title', 'description', 'short_description', 'category', 'project_type', 'location', 'client_name', 'status', 'image_main'])) {
                $data[$field] = sanitizeInput($input[$field]);
            } elseif ($field === 'featured') {
                $data[$field] = $input[$field] ? 1 : 0;
            } elseif ($field === 'budget') {
                $data[$field] = $input[$field] ? floatval($input[$field]) : null;
            } else {
                $data[$field] = $input[$field];
            }
        }
    }
    
    // Validate dates if provided
    if (isset($data['start_date']) && $data['start_date'] && !validateDate($data['start_date'])) {
        sendErrorResponse('Invalid start date format', 400);
    }
    
    if (isset($data['end_date']) && $data['end_date'] && !validateDate($data['end_date'])) {
        sendErrorResponse('Invalid end date format', 400);
    }
    
    if (empty($data)) {
        sendErrorResponse('No valid fields to update', 400);
    }
    
    try {
        updateData('projects', $data, 'id = ?', [$projectId]);
        
        // Handle additional images update if provided
        if (isset($input['additional_images']) && is_array($input['additional_images'])) {
            // Remove existing additional images
            deleteData('project_images', 'project_id = ?', [$projectId]);
            
            // Add new images
            foreach ($input['additional_images'] as $index => $imagePath) {
                insertData('project_images', [
                    'project_id' => $projectId,
                    'image_path' => sanitizeInput($imagePath),
                    'sort_order' => $index
                ]);
            }
        }
        
        sendSuccessResponse([], 'Project updated successfully');
        
    } catch (Exception $e) {
        error_log("Update project error: " . $e->getMessage());
        sendErrorResponse('Failed to update project', 500);
    }
}

/**
 * Delete project
 */
function handleDeleteProject() {
    $projectId = $_GET['id'] ?? '';
    
    if (empty($projectId)) {
        sendErrorResponse('Project ID is required', 400);
    }
    
    // Check if project exists
    $existingProject = fetchOne("SELECT * FROM projects WHERE id = ?", [$projectId]);
    if (!$existingProject) {
        sendErrorResponse('Project not found', 404);
    }
    
    try {
        // Delete project (cascade will handle project_images)
        deleteData('projects', 'id = ?', [$projectId]);
        
        // TODO: Delete associated image files from filesystem
        
        sendSuccessResponse([], 'Project deleted successfully');
        
    } catch (Exception $e) {
        error_log("Delete project error: " . $e->getMessage());
        sendErrorResponse('Failed to delete project', 500);
    }
}

/**
 * Validate date format
 */
function validateDate($date, $format = 'Y-m-d') {
    $d = DateTime::createFromFormat($format, $date);
    return $d && $d->format($format) === $date;
}

/**
 * Get project statistics
 */
if ($_SERVER['REQUEST_METHOD'] === 'GET' && isset($_GET['action']) && $_GET['action'] === 'stats') {
    try {
        $stats = [
            'total_projects' => fetchOne("SELECT COUNT(*) as count FROM projects")['count'],
            'completed_projects' => fetchOne("SELECT COUNT(*) as count FROM projects WHERE category = 'completed'")['count'],
            'ongoing_projects' => fetchOne("SELECT COUNT(*) as count FROM projects WHERE category = 'ongoing'")['count'],
            'featured_projects' => fetchOne("SELECT COUNT(*) as count FROM projects WHERE featured = 1")['count'],
            'projects_by_type' => fetchAll("SELECT project_type, COUNT(*) as count FROM projects WHERE project_type IS NOT NULL AND project_type != '' GROUP BY project_type"),
            'recent_projects' => fetchAll("SELECT id, title, category, created_at FROM projects ORDER BY created_at DESC LIMIT 5")
        ];
        
        sendSuccessResponse($stats);
        
    } catch (Exception $e) {
        error_log("Project stats error: " . $e->getMessage());
        sendErrorResponse('Failed to get project statistics', 500);
    }
}
?>

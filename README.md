# Flori Construction Website & Mobile Admin App

A comprehensive construction company website with PHP/MySQL backend and React Native mobile admin application.

## 🏗️ Project Overview

This project consists of three main components:

1. **Frontend Website** - Modern, responsive website built with PHP, HTML, CSS, and JavaScript
2. **Admin Panel** - Web-based admin interface for content management
3. **Mobile Admin App** - React Native application for managing content on the go

## 🚀 Features

### Website Features
- **Homepage** with hero section, services overview, and featured projects
- **About Us** page with company information
- **Projects Gallery** with completed and ongoing projects
- **Services** showcase with detailed descriptions
- **Media Gallery** for images and videos
- **Contact Form** with message management
- **Responsive Design** optimized for all devices
- **SEO Optimized** with meta tags and structured data

### Admin Panel Features
- **Dashboard** with statistics and quick actions
- **Project Management** - Add, edit, delete projects with image galleries
- **Service Management** - Manage service offerings
- **Media Management** - Upload and organize images/videos
- **Message Management** - View and respond to contact form submissions
- **User Management** - Admin user accounts
- **Settings** - Company information and website settings

### Mobile App Features
- **Authentication** - Secure login with JWT tokens
- **Dashboard** - Overview of projects, services, and messages
- **Project Management** - Full CRUD operations with image upload
- **Service Management** - Add and edit services
- **Media Upload** - Camera and gallery integration
- **Message Inbox** - View and manage customer messages
- **Push Notifications** - Real-time alerts for new messages
- **Offline Support** - Basic functionality when offline

## 🛠️ Technology Stack

### Backend
- **PHP 8.1+** - Server-side scripting
- **MySQL 8.0+** - Database management
- **RESTful API** - JSON-based API for mobile app

### Frontend Website
- **HTML5** - Semantic markup
- **CSS3** - Modern styling with Flexbox/Grid
- **Bootstrap 5** - Responsive framework
- **JavaScript (ES6+)** - Interactive functionality
- **jQuery** - DOM manipulation and AJAX

### Mobile App
- **React Native** - Cross-platform mobile development
- **Expo** - Development platform and tools
- **React Navigation** - Navigation library
- **React Native Paper** - Material Design components
- **Axios** - HTTP client for API calls
- **AsyncStorage** - Local data persistence

## 📋 Prerequisites

### For Website & Admin Panel
- PHP 8.1 or higher
- MySQL 8.0 or higher
- Apache/Nginx web server
- Composer (for PHP dependencies)

### For Mobile App
- Node.js 16+ and npm/yarn
- Expo CLI
- Android Studio (for Android development)
- Xcode (for iOS development, macOS only)

## 🔧 Installation

### 1. Database Setup

```sql
-- Import the database schema
mysql -u root -p < database/schema.sql
```

### 2. Website Configuration

1. Copy files to your web server directory (e.g., `/var/www/html/` or `C:\xampp\htdocs\`)

2. Update database configuration in `includes/database.php`:
```php
$db_config = [
    'host' => 'localhost',
    'dbname' => 'flori_construction',
    'username' => 'your_username',
    'password' => 'your_password',
];
```

3. Set up file permissions:
```bash
chmod 755 uploads/
chmod 644 includes/database.php
```

4. Configure your web server to point to the project directory

### 3. Mobile App Setup

1. Navigate to the mobile app directory:
```bash
cd mobile-app
```

2. Install dependencies:
```bash
npm install
```

3. Update API configuration in `src/services/apiService.js`:
```javascript
const API_BASE_URL = 'https://your-domain.com/api';
```

4. Start the development server:
```bash
npm start
```

## 🔐 Default Admin Credentials

- **Username:** admin
- **Password:** admin123

⚠️ **Important:** Change these credentials immediately after installation!

## 📱 Mobile App Development

### Running on iOS Simulator
```bash
npm run ios
```

### Running on Android Emulator
```bash
npm run android
```

### Building for Production

#### Android
```bash
expo build:android
```

#### iOS
```bash
expo build:ios
```

## 🔧 Configuration

### Environment Variables

Create a `.env` file in the root directory:

```env
# Database Configuration
DB_HOST=localhost
DB_NAME=flori_construction
DB_USER=your_username
DB_PASS=your_password

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password

# API Configuration
API_BASE_URL=https://your-domain.com/api
JWT_SECRET=your_jwt_secret_key

# File Upload
MAX_FILE_SIZE=10485760
ALLOWED_EXTENSIONS=jpg,jpeg,png,gif,webp,mp4,webm
```

### Company Settings

Update company information in the admin panel under Settings, or directly in the database:

```sql
UPDATE settings SET setting_value = 'Your Company Name' WHERE setting_key = 'company_name';
UPDATE settings SET setting_value = '<EMAIL>' WHERE setting_key = 'company_email';
```

## 📊 API Documentation

### Authentication Endpoints

#### Login
```http
POST /api/auth.php
Content-Type: application/json

{
  "username": "admin",
  "password": "admin123"
}
```

#### Validate Token
```http
GET /api/auth.php
Authorization: Bearer {token}
```

### Projects Endpoints

#### Get Projects
```http
GET /api/projects.php?page=1&limit=10&category=completed
Authorization: Bearer {token}
```

#### Create Project
```http
POST /api/projects.php
Authorization: Bearer {token}
Content-Type: application/json

{
  "title": "New Project",
  "description": "Project description",
  "category": "completed",
  "project_type": "Civil Engineering"
}
```

### Services Endpoints

#### Get Services
```http
GET /api/services.php?active=true
Authorization: Bearer {token}
```

#### Create Service
```http
POST /api/services.php
Authorization: Bearer {token}
Content-Type: application/json

{
  "name": "New Service",
  "description": "Service description",
  "icon": "fas fa-tools"
}
```

## 🔒 Security Features

- **SQL Injection Protection** - Prepared statements
- **XSS Protection** - Input sanitization
- **CSRF Protection** - Token validation
- **File Upload Security** - Type and size validation
- **JWT Authentication** - Secure API access
- **Rate Limiting** - Prevent abuse
- **Password Hashing** - bcrypt encryption

## 🚀 Deployment

### Website Deployment

1. **Shared Hosting:**
   - Upload files via FTP
   - Import database via phpMyAdmin
   - Update configuration files

2. **VPS/Dedicated Server:**
   - Set up LAMP/LEMP stack
   - Configure virtual hosts
   - Set up SSL certificates
   - Configure backups

### Mobile App Deployment

1. **Google Play Store:**
   - Build signed APK
   - Create Play Console account
   - Upload and configure app listing

2. **Apple App Store:**
   - Build for iOS
   - Create Apple Developer account
   - Submit via App Store Connect

## 📈 Performance Optimization

### Website
- **Image Optimization** - WebP format, lazy loading
- **CSS/JS Minification** - Reduce file sizes
- **Caching** - Browser and server-side caching
- **CDN Integration** - Content delivery network
- **Database Optimization** - Indexes and query optimization

### Mobile App
- **Code Splitting** - Lazy load components
- **Image Caching** - Reduce network requests
- **Offline Storage** - AsyncStorage for critical data
- **Bundle Size** - Remove unused dependencies

## 🧪 Testing

### Backend Testing
```bash
# Run PHP unit tests
./vendor/bin/phpunit tests/

# Test API endpoints
curl -X POST http://localhost/api/auth.php \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"admin123"}'
```

### Mobile App Testing
```bash
# Run Jest tests
npm test

# Run E2E tests with Detox
npm run test:e2e
```

## 📝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support and questions:

- **Email:** <EMAIL>
- **Phone:** 0208 914 7883
- **Website:** https://floriconstructionltd.com

## 🙏 Acknowledgments

- Bootstrap team for the responsive framework
- React Native community for the mobile framework
- Font Awesome for the icon library
- All contributors and testers

---

**Flori Construction Ltd** - Building Excellence, Delivering Dreams

<?php
/**
 * Media API for Mobile App
 * Handles media upload, management, and gallery operations
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

require_once __DIR__ . '/../includes/database.php';
require_once __DIR__ . '/../includes/functions.php';

// Authenticate user for write operations
$method = $_SERVER['REQUEST_METHOD'];
if (in_array($method, ['POST', 'PUT', 'DELETE'])) {
    $authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? '';
    $tokenData = validateApiToken($authHeader);
    
    if (!$tokenData) {
        sendErrorResponse('Unauthorized', 401);
    }
}

try {
    switch ($method) {
        case 'GET':
            handleGetMedia();
            break;
            
        case 'POST':
            handleUploadMedia();
            break;
            
        case 'PUT':
            handleUpdateMedia();
            break;
            
        case 'DELETE':
            handleDeleteMedia();
            break;
            
        default:
            sendErrorResponse('Method not allowed', 405);
    }
} catch (Exception $e) {
    error_log("Media API Error: " . $e->getMessage());
    sendErrorResponse('Internal server error', 500);
}

/**
 * Get media with optional filtering and pagination
 */
function handleGetMedia() {
    $page = intval($_GET['page'] ?? 1);
    $limit = intval($_GET['limit'] ?? 20);
    $category = $_GET['category'] ?? '';
    $type = $_GET['type'] ?? '';
    $featured = $_GET['featured'] ?? '';
    $search = $_GET['search'] ?? '';
    $mediaId = $_GET['id'] ?? '';
    
    // If specific media ID is requested
    if (!empty($mediaId)) {
        $media = fetchOne("SELECT * FROM media WHERE id = ?", [$mediaId]);
        
        if (!$media) {
            sendErrorResponse('Media not found', 404);
        }
        
        sendSuccessResponse($media);
    }
    
    // Build query conditions
    $conditions = [];
    $params = [];
    
    if (!empty($category)) {
        $conditions[] = "category = ?";
        $params[] = $category;
    }
    
    if (!empty($type)) {
        $conditions[] = "file_type = ?";
        $params[] = $type;
    }
    
    if (!empty($featured)) {
        $conditions[] = "is_featured = ?";
        $params[] = $featured === 'true' ? 1 : 0;
    }
    
    if (!empty($search)) {
        $conditions[] = "(title LIKE ? OR description LIKE ?)";
        $searchTerm = "%{$search}%";
        $params[] = $searchTerm;
        $params[] = $searchTerm;
    }
    
    $whereClause = !empty($conditions) ? 'WHERE ' . implode(' AND ', $conditions) : '';
    
    $sql = "SELECT m.*, u.username as uploaded_by_name
            FROM media m
            LEFT JOIN users u ON m.uploaded_by = u.id
            {$whereClause}
            ORDER BY m.created_at DESC";
    
    $result = paginate($sql, $params, $page, $limit);
    
    sendSuccessResponse($result);
}

/**
 * Handle media upload
 */
function handleUploadMedia() {
    global $tokenData;
    
    // Check if file was uploaded
    if (!isset($_FILES['file']) || $_FILES['file']['error'] !== UPLOAD_ERR_OK) {
        sendErrorResponse('No file uploaded or upload error', 400);
    }
    
    $file = $_FILES['file'];
    $title = sanitizeInput($_POST['title'] ?? '');
    $description = sanitizeInput($_POST['description'] ?? '');
    $category = sanitizeInput($_POST['category'] ?? 'general');
    $relatedId = intval($_POST['related_id'] ?? 0) ?: null;
    $altText = sanitizeInput($_POST['alt_text'] ?? '');
    $isFeatured = isset($_POST['is_featured']) ? ($_POST['is_featured'] === 'true' ? 1 : 0) : 0;
    
    try {
        // Determine file type
        $allowedImageTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
        $allowedVideoTypes = ['video/mp4', 'video/webm', 'video/ogg'];
        
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mimeType = finfo_file($finfo, $file['tmp_name']);
        finfo_close($finfo);
        
        $fileType = 'image';
        $allowedTypes = $allowedImageTypes;
        
        if (in_array($mimeType, $allowedVideoTypes)) {
            $fileType = 'video';
            $allowedTypes = array_merge($allowedImageTypes, $allowedVideoTypes);
        }
        
        // Upload file
        $uploadDir = "uploads/{$category}/";
        $filePath = uploadFile($file, $uploadDir, $allowedTypes);
        
        // Create thumbnail for images
        $thumbnailPath = null;
        if ($fileType === 'image') {
            $thumbnailPath = createThumbnail($filePath);
        }
        
        // Save to database
        $mediaData = [
            'title' => $title ?: pathinfo($file['name'], PATHINFO_FILENAME),
            'description' => $description,
            'file_path' => $filePath,
            'file_type' => $fileType,
            'file_size' => $file['size'],
            'mime_type' => $mimeType,
            'category' => $category,
            'related_id' => $relatedId,
            'alt_text' => $altText,
            'is_featured' => $isFeatured,
            'uploaded_by' => $tokenData['user_id']
        ];
        
        $mediaId = insertData('media', $mediaData);
        
        sendSuccessResponse([
            'id' => $mediaId,
            'file_path' => $filePath,
            'thumbnail_path' => $thumbnailPath,
            'file_type' => $fileType,
            'file_size' => formatFileSize($file['size'])
        ], 'Media uploaded successfully');
        
    } catch (Exception $e) {
        error_log("Media upload error: " . $e->getMessage());
        sendErrorResponse($e->getMessage(), 500);
    }
}

/**
 * Update media metadata
 */
function handleUpdateMedia() {
    $mediaId = $_GET['id'] ?? '';
    
    if (empty($mediaId)) {
        sendErrorResponse('Media ID is required', 400);
    }
    
    // Check if media exists
    $existingMedia = fetchOne("SELECT * FROM media WHERE id = ?", [$mediaId]);
    if (!$existingMedia) {
        sendErrorResponse('Media not found', 404);
    }
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    // Build update data (only include provided fields)
    $data = [];
    $allowedFields = ['title', 'description', 'category', 'related_id', 'alt_text', 'is_featured'];
    
    foreach ($allowedFields as $field) {
        if (isset($input[$field])) {
            if (in_array($field, ['title', 'description', 'category', 'alt_text'])) {
                $data[$field] = sanitizeInput($input[$field]);
            } elseif ($field === 'related_id') {
                $data[$field] = intval($input[$field]) ?: null;
            } elseif ($field === 'is_featured') {
                $data[$field] = $input[$field] ? 1 : 0;
            }
        }
    }
    
    if (empty($data)) {
        sendErrorResponse('No valid fields to update', 400);
    }
    
    try {
        updateData('media', $data, 'id = ?', [$mediaId]);
        sendSuccessResponse([], 'Media updated successfully');
        
    } catch (Exception $e) {
        error_log("Update media error: " . $e->getMessage());
        sendErrorResponse('Failed to update media', 500);
    }
}

/**
 * Delete media
 */
function handleDeleteMedia() {
    $mediaId = $_GET['id'] ?? '';
    
    if (empty($mediaId)) {
        sendErrorResponse('Media ID is required', 400);
    }
    
    // Check if media exists
    $existingMedia = fetchOne("SELECT * FROM media WHERE id = ?", [$mediaId]);
    if (!$existingMedia) {
        sendErrorResponse('Media not found', 404);
    }
    
    try {
        // Delete file from filesystem
        if (file_exists($existingMedia['file_path'])) {
            unlink($existingMedia['file_path']);
        }
        
        // Delete thumbnail if exists
        $thumbnailPath = getThumbnailPath($existingMedia['file_path']);
        if (file_exists($thumbnailPath)) {
            unlink($thumbnailPath);
        }
        
        // Delete from database
        deleteData('media', 'id = ?', [$mediaId]);
        
        sendSuccessResponse([], 'Media deleted successfully');
        
    } catch (Exception $e) {
        error_log("Delete media error: " . $e->getMessage());
        sendErrorResponse('Failed to delete media', 500);
    }
}

/**
 * Bulk delete media
 */
if ($_SERVER['REQUEST_METHOD'] === 'DELETE' && isset($_GET['action']) && $_GET['action'] === 'bulk') {
    $authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? '';
    $tokenData = validateApiToken($authHeader);
    
    if (!$tokenData) {
        sendErrorResponse('Unauthorized', 401);
    }
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (empty($input['ids']) || !is_array($input['ids'])) {
        sendErrorResponse('Media IDs array is required', 400);
    }
    
    $deletedCount = 0;
    $errors = [];
    
    foreach ($input['ids'] as $mediaId) {
        try {
            $existingMedia = fetchOne("SELECT * FROM media WHERE id = ?", [$mediaId]);
            if ($existingMedia) {
                // Delete file from filesystem
                if (file_exists($existingMedia['file_path'])) {
                    unlink($existingMedia['file_path']);
                }
                
                // Delete thumbnail if exists
                $thumbnailPath = getThumbnailPath($existingMedia['file_path']);
                if (file_exists($thumbnailPath)) {
                    unlink($thumbnailPath);
                }
                
                // Delete from database
                deleteData('media', 'id = ?', [$mediaId]);
                $deletedCount++;
            }
        } catch (Exception $e) {
            $errors[] = "Failed to delete media ID {$mediaId}: " . $e->getMessage();
        }
    }
    
    sendSuccessResponse([
        'deleted_count' => $deletedCount,
        'errors' => $errors
    ], "{$deletedCount} media items deleted successfully");
}

/**
 * Get media statistics
 */
if ($_SERVER['REQUEST_METHOD'] === 'GET' && isset($_GET['action']) && $_GET['action'] === 'stats') {
    try {
        $stats = [
            'total_media' => fetchOne("SELECT COUNT(*) as count FROM media")['count'],
            'total_images' => fetchOne("SELECT COUNT(*) as count FROM media WHERE file_type = 'image'")['count'],
            'total_videos' => fetchOne("SELECT COUNT(*) as count FROM media WHERE file_type = 'video'")['count'],
            'featured_media' => fetchOne("SELECT COUNT(*) as count FROM media WHERE is_featured = 1")['count'],
            'total_size' => fetchOne("SELECT SUM(file_size) as size FROM media")['size'] ?: 0,
            'media_by_category' => fetchAll("SELECT category, COUNT(*) as count FROM media GROUP BY category"),
            'recent_uploads' => fetchAll("SELECT id, title, file_type, created_at FROM media ORDER BY created_at DESC LIMIT 10")
        ];
        
        $stats['total_size_formatted'] = formatFileSize($stats['total_size']);
        
        sendSuccessResponse($stats);
        
    } catch (Exception $e) {
        error_log("Media stats error: " . $e->getMessage());
        sendErrorResponse('Failed to get media statistics', 500);
    }
}

/**
 * Get media categories
 */
if ($_SERVER['REQUEST_METHOD'] === 'GET' && isset($_GET['action']) && $_GET['action'] === 'categories') {
    try {
        $categories = fetchAll("SELECT category, COUNT(*) as count FROM media GROUP BY category ORDER BY category");
        sendSuccessResponse($categories);
        
    } catch (Exception $e) {
        error_log("Media categories error: " . $e->getMessage());
        sendErrorResponse('Failed to get media categories', 500);
    }
}
?>

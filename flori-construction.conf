# Virtual Host Configuration for Flori Construction Website
# Place this file in: C:\xampp\apache\conf\extra\

<VirtualHost *:80>
    # Server configuration
    ServerName flori-construction.local
    ServerAlias www.flori-construction.local
    DocumentRoot "C:/xampp/htdocs/web-flori"
    
    # Directory permissions
    <Directory "C:/xampp/htdocs/web-flori">
        Options Indexes FollowSymLinks Includes ExecCGI
        AllowOverride All
        Require all granted
        DirectoryIndex index.php index.html
    </Directory>
    
    # Logging
    ErrorLog "logs/flori-construction-error.log"
    CustomLog "logs/flori-construction-access.log" combined
    
    # PHP Configuration
    <IfModule mod_php.c>
        php_admin_value upload_max_filesize 32M
        php_admin_value post_max_size 32M
        php_admin_value memory_limit 256M
        php_admin_value max_execution_time 300
        php_admin_value max_input_vars 3000
    </IfModule>
    
    # Security headers
    <IfModule mod_headers.c>
        Header always set X-Content-Type-Options nosniff
        Header always set X-Frame-Options SAMEORIGIN
        Header always set X-XSS-Protection "1; mode=block"
    </IfModule>
    
    # Alias for admin panel
    Alias /admin "C:/xampp/htdocs/web-flori/admin"
    <Directory "C:/xampp/htdocs/web-flori/admin">
        AllowOverride All
        Require all granted
    </Directory>
    
    # Alias for API endpoints
    Alias /api "C:/xampp/htdocs/web-flori/api"
    <Directory "C:/xampp/htdocs/web-flori/api">
        AllowOverride All
        Require all granted
    </Directory>
    
    # Alias for uploads
    Alias /uploads "C:/xampp/htdocs/web-flori/uploads"
    <Directory "C:/xampp/htdocs/web-flori/uploads">
        AllowOverride All
        Require all granted
        # Prevent execution of PHP files in uploads
        <FilesMatch "\.php$">
            Require all denied
        </FilesMatch>
    </Directory>
    
    # Alias for assets
    Alias /assets "C:/xampp/htdocs/web-flori/assets"
    <Directory "C:/xampp/htdocs/web-flori/assets">
        AllowOverride All
        Require all granted
        # Set cache headers for static assets
        <IfModule mod_expires.c>
            ExpiresActive On
            ExpiresByType text/css "access plus 1 year"
            ExpiresByType application/javascript "access plus 1 year"
            ExpiresByType image/png "access plus 1 year"
            ExpiresByType image/jpg "access plus 1 year"
            ExpiresByType image/jpeg "access plus 1 year"
            ExpiresByType image/gif "access plus 1 year"
            ExpiresByType image/svg+xml "access plus 1 year"
        </IfModule>
    </Directory>
</VirtualHost>

# HTTPS Virtual Host (uncomment when SSL certificate is available)
# <VirtualHost *:443>
#     ServerName flori-construction.local
#     ServerAlias www.flori-construction.local
#     DocumentRoot "C:/xampp/htdocs/web-flori"
#     
#     SSLEngine on
#     SSLCertificateFile "conf/ssl.crt/server.crt"
#     SSLCertificateKeyFile "conf/ssl.key/server.key"
#     
#     <Directory "C:/xampp/htdocs/web-flori">
#         Options Indexes FollowSymLinks Includes ExecCGI
#         AllowOverride All
#         Require all granted
#         DirectoryIndex index.php index.html
#     </Directory>
#     
#     ErrorLog "logs/flori-construction-ssl-error.log"
#     CustomLog "logs/flori-construction-ssl-access.log" combined
# </VirtualHost>

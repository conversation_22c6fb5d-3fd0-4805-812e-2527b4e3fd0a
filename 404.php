<?php
$pageTitle = '404 - Page Not Found';
$pageDescription = 'The page you are looking for could not be found.';

require_once 'includes/header.php';
?>

<div class="error-page py-5">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8 text-center">
                <div class="error-content">
                    <div class="error-code mb-4">
                        <h1 class="display-1 fw-bold text-primary">404</h1>
                    </div>
                    
                    <div class="error-message mb-4">
                        <h2 class="h3 mb-3">Oops! Page Not Found</h2>
                        <p class="lead text-muted mb-4">
                            The page you are looking for might have been removed, had its name changed, or is temporarily unavailable.
                        </p>
                    </div>
                    
                    <div class="error-actions">
                        <a href="/" class="btn btn-primary btn-lg me-3">
                            <i class="fas fa-home me-2"></i>Go Home
                        </a>
                        <a href="/contact.php" class="btn btn-outline-primary btn-lg">
                            <i class="fas fa-envelope me-2"></i>Contact Us
                        </a>
                    </div>
                    
                    <div class="error-suggestions mt-5">
                        <h4 class="mb-3">You might be interested in:</h4>
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <a href="/services.php" class="text-decoration-none">
                                    <div class="card h-100 border-0 shadow-sm">
                                        <div class="card-body text-center">
                                            <i class="fas fa-tools fa-2x text-primary mb-3"></i>
                                            <h5 class="card-title">Our Services</h5>
                                            <p class="card-text text-muted">Explore our construction services</p>
                                        </div>
                                    </div>
                                </a>
                            </div>
                            <div class="col-md-4 mb-3">
                                <a href="/projects.php" class="text-decoration-none">
                                    <div class="card h-100 border-0 shadow-sm">
                                        <div class="card-body text-center">
                                            <i class="fas fa-building fa-2x text-primary mb-3"></i>
                                            <h5 class="card-title">Our Projects</h5>
                                            <p class="card-text text-muted">View our completed work</p>
                                        </div>
                                    </div>
                                </a>
                            </div>
                            <div class="col-md-4 mb-3">
                                <a href="/about.php" class="text-decoration-none">
                                    <div class="card h-100 border-0 shadow-sm">
                                        <div class="card-body text-center">
                                            <i class="fas fa-info-circle fa-2x text-primary mb-3"></i>
                                            <h5 class="card-title">About Us</h5>
                                            <p class="card-text text-muted">Learn about our company</p>
                                        </div>
                                    </div>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.error-page {
    min-height: 60vh;
    display: flex;
    align-items: center;
}

.error-code h1 {
    font-size: 8rem;
    line-height: 1;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
}

.card:hover {
    transform: translateY(-5px);
    transition: transform 0.3s ease;
}

@media (max-width: 768px) {
    .error-code h1 {
        font-size: 6rem;
    }
}
</style>

<?php require_once 'includes/footer.php'; ?>

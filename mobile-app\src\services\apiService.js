import axios from 'axios';

// API Configuration
const API_BASE_URL = 'https://your-domain.com/api'; // Replace with your actual API URL

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    // Add auth token if available
    if (config.token) {
      config.headers.Authorization = `Bearer ${config.token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => {
    return response.data;
  },
  (error) => {
    if (error.response?.status === 401) {
      // Token expired or invalid
      // This could trigger a logout in the app
    }
    return Promise.reject(error);
  }
);

// API Service
export const apiService = {
  // Authentication
  login: async (username, password) => {
    return await api.post('/auth.php', { username, password });
  },

  logout: async (token) => {
    return await api.delete('/auth.php', { token });
  },

  validateToken: async (token) => {
    return await api.get('/auth.php', { token });
  },

  changePassword: async (token, currentPassword, newPassword) => {
    return await api.post('/auth.php?action=change-password', {
      current_password: currentPassword,
      new_password: newPassword,
    }, { token });
  },

  // Projects
  getProjects: async (token, params = {}) => {
    const queryString = new URLSearchParams(params).toString();
    return await api.get(`/projects.php?${queryString}`, { token });
  },

  getProject: async (token, id) => {
    return await api.get(`/projects.php?id=${id}`, { token });
  },

  createProject: async (token, projectData) => {
    return await api.post('/projects.php', projectData, { token });
  },

  updateProject: async (token, id, projectData) => {
    return await api.put(`/projects.php?id=${id}`, projectData, { token });
  },

  deleteProject: async (token, id) => {
    return await api.delete(`/projects.php?id=${id}`, { token });
  },

  getProjectStats: async (token) => {
    return await api.get('/projects.php?action=stats', { token });
  },

  // Services
  getServices: async (token, params = {}) => {
    const queryString = new URLSearchParams(params).toString();
    return await api.get(`/services.php?${queryString}`, { token });
  },

  getService: async (token, id) => {
    return await api.get(`/services.php?id=${id}`, { token });
  },

  createService: async (token, serviceData) => {
    return await api.post('/services.php', serviceData, { token });
  },

  updateService: async (token, id, serviceData) => {
    return await api.put(`/services.php?id=${id}`, serviceData, { token });
  },

  deleteService: async (token, id) => {
    return await api.delete(`/services.php?id=${id}`, { token });
  },

  reorderServices: async (token, services) => {
    return await api.put('/services.php?action=reorder', { services }, { token });
  },

  toggleServiceStatus: async (token, id) => {
    return await api.put(`/services.php?action=toggle-status&id=${id}`, {}, { token });
  },

  getServiceStats: async (token) => {
    return await api.get('/services.php?action=stats', { token });
  },

  // Media
  getMedia: async (token, params = {}) => {
    const queryString = new URLSearchParams(params).toString();
    return await api.get(`/media.php?${queryString}`, { token });
  },

  getMediaItem: async (token, id) => {
    return await api.get(`/media.php?id=${id}`, { token });
  },

  uploadMedia: async (token, formData) => {
    return await api.post('/media.php', formData, {
      token,
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },

  updateMedia: async (token, id, mediaData) => {
    return await api.put(`/media.php?id=${id}`, mediaData, { token });
  },

  deleteMedia: async (token, id) => {
    return await api.delete(`/media.php?id=${id}`, { token });
  },

  bulkDeleteMedia: async (token, ids) => {
    return await api.delete('/media.php?action=bulk', { ids }, { token });
  },

  getMediaStats: async (token) => {
    return await api.get('/media.php?action=stats', { token });
  },

  getMediaCategories: async (token) => {
    return await api.get('/media.php?action=categories', { token });
  },

  // Messages
  getMessages: async (token, params = {}) => {
    const queryString = new URLSearchParams(params).toString();
    return await api.get(`/messages.php?${queryString}`, { token });
  },

  getMessage: async (token, id) => {
    return await api.get(`/messages.php?id=${id}`, { token });
  },

  updateMessageStatus: async (token, id, status) => {
    return await api.put(`/messages.php?id=${id}`, { status }, { token });
  },

  deleteMessage: async (token, id) => {
    return await api.delete(`/messages.php?id=${id}`, { token });
  },

  bulkUpdateMessages: async (token, ids, status) => {
    return await api.put('/messages.php?action=bulk-update', { ids, status }, { token });
  },

  getMessageStats: async (token) => {
    return await api.get('/messages.php?action=stats', { token });
  },

  // Contact form (public endpoint)
  sendContactMessage: async (messageData) => {
    return await api.post('/messages.php?action=contact', messageData);
  },

  // Dashboard
  getDashboardStats: async (token) => {
    const [projectStats, serviceStats, mediaStats, messageStats] = await Promise.all([
      apiService.getProjectStats(token),
      apiService.getServiceStats(token),
      apiService.getMediaStats(token),
      apiService.getMessageStats(token),
    ]);

    return {
      projects: projectStats.data,
      services: serviceStats.data,
      media: mediaStats.data,
      messages: messageStats.data,
    };
  },

  // File upload helper
  createFormData: (file, additionalData = {}) => {
    const formData = new FormData();
    
    // Add file
    formData.append('file', {
      uri: file.uri,
      type: file.type || 'image/jpeg',
      name: file.fileName || 'image.jpg',
    });
    
    // Add additional data
    Object.keys(additionalData).forEach(key => {
      formData.append(key, additionalData[key]);
    });
    
    return formData;
  },

  // Error handling helper
  handleApiError: (error) => {
    if (error.response) {
      // Server responded with error status
      return {
        success: false,
        message: error.response.data?.message || 'Server error',
        status: error.response.status,
      };
    } else if (error.request) {
      // Network error
      return {
        success: false,
        message: 'Network error. Please check your connection.',
        status: 0,
      };
    } else {
      // Other error
      return {
        success: false,
        message: error.message || 'An unexpected error occurred',
        status: -1,
      };
    }
  },
};

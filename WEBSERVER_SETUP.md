# Web Server Configuration Guide
## Flori Construction Website

This guide will help you configure your XAMPP web server to properly serve the Flori Construction website.

## Prerequisites

- XAMPP installed (with Apache and MySQL)
- Administrator privileges on Windows
- Project files located in `C:\xampp\htdocs\web-flori`

## Quick Setup (Automated)

1. **Run the setup script as Administrator:**
   - Right-click on `setup-webserver.bat`
   - Select "Run as administrator"
   - Follow the prompts

2. **Restart Apache in XAMPP Control Panel**

3. **Import the database:**
   - Open phpMyAdmin (http://localhost/phpmyadmin)
   - Create database: `flori_construction`
   - Import `database/schema.sql`

4. **Access your website:**
   - Main site: http://flori-construction.local
   - Admin panel: http://flori-construction.local/admin

## Manual Setup

If you prefer to configure manually or the automated script doesn't work:

### 1. Virtual Host Configuration

Copy `flori-construction.conf` to `C:\xampp\apache\conf\extra\`

### 2. Update Apache Configuration

Edit `C:\xampp\apache\conf\httpd.conf` and add at the end:
```apache
# Flori Construction Virtual Host
Include conf/extra/flori-construction.conf
```

### 3. Update Hosts File

Edit `C:\Windows\System32\drivers\etc\hosts` as Administrator and add:
```
127.0.0.1    flori-construction.local
127.0.0.1    www.flori-construction.local
```

### 4. Enable Required Apache Modules

Ensure these modules are enabled in `httpd.conf`:
```apache
LoadModule rewrite_module modules/mod_rewrite.so
LoadModule headers_module modules/mod_headers.so
LoadModule expires_module modules/mod_expires.so
LoadModule deflate_module modules/mod_deflate.so
```

### 5. Set Directory Permissions

Ensure the `uploads` directory has write permissions:
```cmd
icacls "uploads" /grant "Everyone:(OI)(CI)F" /T
```

## Configuration Files Created

1. **`.htaccess`** - URL rewriting, security headers, and performance optimization
2. **`flori-construction.conf`** - Apache virtual host configuration
3. **`404.php`** - Custom 404 error page
4. **`500.php`** - Custom 500 error page
5. **`setup-webserver.bat`** - Automated setup script

## Security Features

- Protection of sensitive directories (`includes/`, `database/`)
- Prevention of direct access to configuration files
- XSS and clickjacking protection headers
- Upload directory PHP execution prevention
- Hidden file access denial

## Performance Features

- Gzip compression for text files
- Browser caching for static assets
- Optimized cache headers
- Clean URLs (removes .php extension)

## Troubleshooting

### Common Issues:

1. **"Access Denied" errors:**
   - Ensure you ran the setup as Administrator
   - Check directory permissions

2. **Virtual host not working:**
   - Verify Apache configuration syntax: `httpd -t`
   - Check if virtual host include is added to httpd.conf
   - Restart Apache service

3. **Database connection errors:**
   - Ensure MySQL is running in XAMPP
   - Verify database name and credentials in `includes/database.php`
   - Import the database schema

4. **404 errors for assets:**
   - Check file paths in your PHP files
   - Ensure assets directory exists and has proper permissions

### Testing the Configuration:

1. **Check Apache configuration:**
   ```cmd
   C:\xampp\apache\bin\httpd.exe -t
   ```

2. **Test virtual host:**
   ```cmd
   ping flori-construction.local
   ```
   Should return 127.0.0.1

3. **Check website access:**
   - http://flori-construction.local (should load homepage)
   - http://flori-construction.local/admin (should load admin login)

## Additional Configuration

### SSL/HTTPS Setup (Optional)

To enable HTTPS:
1. Generate SSL certificate or use self-signed certificate
2. Uncomment HTTPS virtual host section in `flori-construction.conf`
3. Update certificate paths
4. Uncomment HTTPS redirect in `.htaccess`

### Production Deployment

For production deployment:
1. Update database credentials
2. Enable HTTPS redirect
3. Configure proper error logging
4. Set up regular backups
5. Implement additional security measures

## Support

If you encounter issues:
1. Check Apache error logs: `C:\xampp\apache\logs\error.log`
2. Check virtual host specific logs: `C:\xampp\apache\logs\flori-construction-error.log`
3. Verify PHP error logs
4. Test with a simple PHP file first

## File Structure

```
web-flori/
├── .htaccess                 # Apache configuration
├── flori-construction.conf   # Virtual host config
├── setup-webserver.bat      # Setup script
├── 404.php                  # Error page
├── 500.php                  # Error page
├── index.php                # Homepage
├── admin/                   # Admin panel
├── api/                     # API endpoints
├── assets/                  # Static files
├── database/                # Database files
├── includes/                # PHP includes
└── uploads/                 # File uploads
```

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    
    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Custom Admin JS -->
    <script>
        $(document).ready(function() {
            // Initialize DataTables
            $('.data-table').DataTable({
                responsive: true,
                pageLength: 25,
                order: [[0, 'desc']],
                language: {
                    search: "Search:",
                    lengthMenu: "Show _MENU_ entries",
                    info: "Showing _START_ to _END_ of _TOTAL_ entries",
                    paginate: {
                        first: "First",
                        last: "Last",
                        next: "Next",
                        previous: "Previous"
                    }
                }
            });
            
            // Initialize tooltips
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
            
            // Initialize popovers
            var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
            var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
                return new bootstrap.Popover(popoverTriggerEl);
            });
            
            // Confirm delete actions
            $('.btn-delete').on('click', function(e) {
                if (!confirm('Are you sure you want to delete this item? This action cannot be undone.')) {
                    e.preventDefault();
                }
            });
            
            // Auto-hide alerts after 5 seconds
            $('.alert:not(.alert-permanent)').delay(5000).fadeOut();
            
            // Form validation
            $('.needs-validation').on('submit', function(event) {
                if (this.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                $(this).addClass('was-validated');
            });
            
            // File upload preview
            $('input[type="file"]').on('change', function() {
                const file = this.files[0];
                const preview = $(this).siblings('.file-preview');
                
                if (file && file.type.startsWith('image/')) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        preview.html('<img src="' + e.target.result + '" class="img-thumbnail" style="max-width: 200px;">');
                    };
                    reader.readAsDataURL(file);
                } else {
                    preview.html('');
                }
            });
            
            // Bulk actions
            $('#selectAll').on('change', function() {
                $('.item-checkbox').prop('checked', this.checked);
                toggleBulkActions();
            });
            
            $('.item-checkbox').on('change', function() {
                toggleBulkActions();
            });
            
            function toggleBulkActions() {
                const checkedItems = $('.item-checkbox:checked').length;
                if (checkedItems > 0) {
                    $('.bulk-actions').show();
                    $('.bulk-count').text(checkedItems);
                } else {
                    $('.bulk-actions').hide();
                }
            }
            
            // AJAX form submission
            $('.ajax-form').on('submit', function(e) {
                e.preventDefault();
                
                const form = $(this);
                const submitBtn = form.find('button[type="submit"]');
                const originalText = submitBtn.html();
                
                // Show loading state
                submitBtn.html('<i class="fas fa-spinner fa-spin me-2"></i>Saving...').prop('disabled', true);
                
                $.ajax({
                    url: form.attr('action'),
                    type: form.attr('method'),
                    data: new FormData(this),
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        if (response.success) {
                            showAlert('success', response.message || 'Operation completed successfully');
                            if (response.redirect) {
                                setTimeout(() => {
                                    window.location.href = response.redirect;
                                }, 1000);
                            }
                        } else {
                            showAlert('danger', response.message || 'An error occurred');
                        }
                    },
                    error: function() {
                        showAlert('danger', 'An error occurred. Please try again.');
                    },
                    complete: function() {
                        submitBtn.html(originalText).prop('disabled', false);
                    }
                });
            });
            
            // Show alert function
            function showAlert(type, message) {
                const alertHtml = `
                    <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                        ${message}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                `;
                
                $('#alertContainer').html(alertHtml);
                
                // Auto-hide success alerts
                if (type === 'success') {
                    setTimeout(() => {
                        $('.alert').alert('close');
                    }, 3000);
                }
            }
            
            // Search functionality
            $('.search-input').on('keyup', function() {
                const searchTerm = $(this).val().toLowerCase();
                const targetTable = $(this).data('target');
                
                $(targetTable + ' tbody tr').each(function() {
                    const rowText = $(this).text().toLowerCase();
                    if (rowText.includes(searchTerm)) {
                        $(this).show();
                    } else {
                        $(this).hide();
                    }
                });
            });
            
            // Auto-save drafts
            let autoSaveTimer;
            $('.auto-save').on('input', function() {
                clearTimeout(autoSaveTimer);
                autoSaveTimer = setTimeout(() => {
                    saveDraft();
                }, 2000);
            });
            
            function saveDraft() {
                const formData = $('.auto-save-form').serialize();
                $.post('save-draft.php', formData, function(response) {
                    if (response.success) {
                        $('.draft-status').text('Draft saved at ' + new Date().toLocaleTimeString());
                    }
                });
            }
            
            // Image gallery modal
            $('.gallery-image').on('click', function() {
                const imgSrc = $(this).attr('src');
                const imgAlt = $(this).attr('alt');
                
                $('#imageModal .modal-body img').attr('src', imgSrc).attr('alt', imgAlt);
                $('#imageModal .modal-title').text(imgAlt);
                $('#imageModal').modal('show');
            });
            
            // Copy to clipboard
            $('.copy-btn').on('click', function() {
                const text = $(this).data('copy');
                navigator.clipboard.writeText(text).then(() => {
                    showAlert('success', 'Copied to clipboard!');
                });
            });
            
            // Real-time notifications (if WebSocket is available)
            if (typeof WebSocket !== 'undefined') {
                // Initialize WebSocket connection for real-time updates
                // This would connect to a WebSocket server for live notifications
            }
            
            // Keyboard shortcuts
            $(document).on('keydown', function(e) {
                // Ctrl+S to save
                if (e.ctrlKey && e.which === 83) {
                    e.preventDefault();
                    $('.btn-save').click();
                }
                
                // Ctrl+N for new item
                if (e.ctrlKey && e.which === 78) {
                    e.preventDefault();
                    $('.btn-new').click();
                }
            });
        });
        
        // Utility functions
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
        
        function formatDate(date) {
            return new Date(date).toLocaleDateString();
        }
        
        function formatDateTime(date) {
            return new Date(date).toLocaleString();
        }
        
        // Export functions
        function exportData(format, table) {
            window.location.href = `export.php?format=${format}&table=${table}`;
        }
        
        // Print function
        function printPage() {
            window.print();
        }
    </script>
    
    <!-- Page-specific scripts -->
    <?php if (isset($additionalScripts)): ?>
        <?php echo $additionalScripts; ?>
    <?php endif; ?>
    
    <!-- Footer -->
    <footer class="footer mt-auto py-3 bg-light border-top">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <span class="text-muted">
                        &copy; <?php echo date('Y'); ?> <?php echo getSetting('company_name'); ?>. 
                        Admin Panel v1.0
                    </span>
                </div>
                <div class="col-md-6 text-md-end">
                    <span class="text-muted">
                        Last login: <?php echo formatDate($currentUser['last_login'] ?? date('Y-m-d H:i:s')); ?>
                    </span>
                </div>
            </div>
        </div>
    </footer>
</body>
</html>

<?php
require_once __DIR__ . '/../includes/database.php';
require_once __DIR__ . '/../includes/functions.php';

requireLogin();

$pageTitle = 'Dashboard';

// Get dashboard statistics
$stats = [
    'total_projects' => fetchOne("SELECT COUNT(*) as count FROM projects")['count'],
    'completed_projects' => fetchOne("SELECT COUNT(*) as count FROM projects WHERE category = 'completed'")['count'],
    'ongoing_projects' => fetchOne("SELECT COUNT(*) as count FROM projects WHERE category = 'ongoing'")['count'],
    'total_services' => fetchOne("SELECT COUNT(*) as count FROM services WHERE is_active = 1")['count'],
    'total_media' => fetchOne("SELECT COUNT(*) as count FROM media")['count'],
    'new_messages' => fetchOne("SELECT COUNT(*) as count FROM messages WHERE status = 'new'")['count'],
    'total_messages' => fetchOne("SELECT COUNT(*) as count FROM messages")['count']
];

// Get recent activities
$recentProjects = fetchAll("SELECT id, title, category, created_at FROM projects ORDER BY created_at DESC LIMIT 5");
$recentMessages = fetchAll("SELECT id, name, email, subject, status, created_at FROM messages ORDER BY created_at DESC LIMIT 5");
$recentMedia = fetchAll("SELECT id, title, file_type, created_at FROM media ORDER BY created_at DESC LIMIT 5");

include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <?php include 'includes/sidebar.php'; ?>
        
        <!-- Main Content -->
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">Dashboard</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <button type="button" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-download me-1"></i>Export
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-primary shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                        Total Projects
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php echo $stats['total_projects']; ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-building fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-success shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                        Completed Projects
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php echo $stats['completed_projects']; ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-info shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                        Active Services
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php echo $stats['total_services']; ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-tools fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-warning shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                        New Messages
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php echo $stats['new_messages']; ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-envelope fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Recent Activities -->
            <div class="row">
                <!-- Recent Projects -->
                <div class="col-lg-6 mb-4">
                    <div class="card shadow">
                        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                            <h6 class="m-0 font-weight-bold text-primary">Recent Projects</h6>
                            <a href="projects.php" class="btn btn-sm btn-primary">View All</a>
                        </div>
                        <div class="card-body">
                            <?php if (empty($recentProjects)): ?>
                                <p class="text-muted">No projects found.</p>
                            <?php else: ?>
                                <div class="list-group list-group-flush">
                                    <?php foreach ($recentProjects as $project): ?>
                                        <div class="list-group-item d-flex justify-content-between align-items-center">
                                            <div>
                                                <h6 class="mb-1"><?php echo htmlspecialchars($project['title']); ?></h6>
                                                <small class="text-muted">
                                                    <span class="badge badge-<?php echo $project['category'] === 'completed' ? 'success' : 'warning'; ?>">
                                                        <?php echo ucfirst($project['category']); ?>
                                                    </span>
                                                    • <?php echo formatDate($project['created_at']); ?>
                                                </small>
                                            </div>
                                            <a href="projects.php?edit=<?php echo $project['id']; ?>" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                
                <!-- Recent Messages -->
                <div class="col-lg-6 mb-4">
                    <div class="card shadow">
                        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                            <h6 class="m-0 font-weight-bold text-primary">Recent Messages</h6>
                            <a href="messages.php" class="btn btn-sm btn-primary">View All</a>
                        </div>
                        <div class="card-body">
                            <?php if (empty($recentMessages)): ?>
                                <p class="text-muted">No messages found.</p>
                            <?php else: ?>
                                <div class="list-group list-group-flush">
                                    <?php foreach ($recentMessages as $message): ?>
                                        <div class="list-group-item">
                                            <div class="d-flex w-100 justify-content-between">
                                                <h6 class="mb-1">
                                                    <?php echo htmlspecialchars($message['name']); ?>
                                                    <?php if ($message['status'] === 'new'): ?>
                                                        <span class="badge bg-danger">New</span>
                                                    <?php endif; ?>
                                                </h6>
                                                <small><?php echo formatDate($message['created_at']); ?></small>
                                            </div>
                                            <p class="mb-1"><?php echo htmlspecialchars($message['subject']); ?></p>
                                            <small class="text-muted"><?php echo htmlspecialchars($message['email']); ?></small>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Quick Actions -->
            <div class="row">
                <div class="col-12">
                    <div class="card shadow">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Quick Actions</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3 mb-3">
                                    <a href="projects.php?action=add" class="btn btn-primary btn-block">
                                        <i class="fas fa-plus me-2"></i>Add New Project
                                    </a>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <a href="services.php?action=add" class="btn btn-success btn-block">
                                        <i class="fas fa-plus me-2"></i>Add New Service
                                    </a>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <a href="media.php?action=upload" class="btn btn-info btn-block">
                                        <i class="fas fa-upload me-2"></i>Upload Media
                                    </a>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <a href="settings.php" class="btn btn-secondary btn-block">
                                        <i class="fas fa-cog me-2"></i>Settings
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<?php include 'includes/footer.php'; ?>

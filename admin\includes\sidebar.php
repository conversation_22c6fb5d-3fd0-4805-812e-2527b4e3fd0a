<?php
$currentPage = basename($_SERVER['PHP_SELF'], '.php');
?>

<nav id="sidebarMenu" class="col-md-3 col-lg-2 d-md-block bg-dark sidebar collapse">
    <div class="position-sticky pt-3">
        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link <?php echo $currentPage === 'dashboard' ? 'active' : ''; ?>" href="dashboard.php">
                    <i class="fas fa-tachometer-alt"></i>
                    Dashboard
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link <?php echo $currentPage === 'projects' ? 'active' : ''; ?>" href="projects.php">
                    <i class="fas fa-building"></i>
                    Projects
                    <?php
                    $projectCount = fetchOne("SELECT COUNT(*) as count FROM projects")['count'];
                    if ($projectCount > 0):
                    ?>
                        <span class="badge bg-primary ms-auto"><?php echo $projectCount; ?></span>
                    <?php endif; ?>
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link <?php echo $currentPage === 'services' ? 'active' : ''; ?>" href="services.php">
                    <i class="fas fa-tools"></i>
                    Services
                    <?php
                    $serviceCount = fetchOne("SELECT COUNT(*) as count FROM services WHERE is_active = 1")['count'];
                    if ($serviceCount > 0):
                    ?>
                        <span class="badge bg-success ms-auto"><?php echo $serviceCount; ?></span>
                    <?php endif; ?>
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link <?php echo $currentPage === 'media' ? 'active' : ''; ?>" href="media.php">
                    <i class="fas fa-images"></i>
                    Media Gallery
                    <?php
                    $mediaCount = fetchOne("SELECT COUNT(*) as count FROM media")['count'];
                    if ($mediaCount > 0):
                    ?>
                        <span class="badge bg-info ms-auto"><?php echo $mediaCount; ?></span>
                    <?php endif; ?>
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link <?php echo $currentPage === 'messages' ? 'active' : ''; ?>" href="messages.php">
                    <i class="fas fa-envelope"></i>
                    Messages
                    <?php
                    $newMessageCount = fetchOne("SELECT COUNT(*) as count FROM messages WHERE status = 'new'")['count'];
                    if ($newMessageCount > 0):
                    ?>
                        <span class="badge bg-danger ms-auto"><?php echo $newMessageCount; ?></span>
                    <?php endif; ?>
                </a>
            </li>
        </ul>
        
        <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
            <span>Management</span>
        </h6>
        
        <ul class="nav flex-column mb-2">
            <li class="nav-item">
                <a class="nav-link <?php echo $currentPage === 'users' ? 'active' : ''; ?>" href="users.php">
                    <i class="fas fa-users"></i>
                    Users
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link <?php echo $currentPage === 'settings' ? 'active' : ''; ?>" href="settings.php">
                    <i class="fas fa-cog"></i>
                    Settings
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link <?php echo $currentPage === 'backup' ? 'active' : ''; ?>" href="backup.php">
                    <i class="fas fa-database"></i>
                    Backup
                </a>
            </li>
        </ul>
        
        <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
            <span>Mobile App</span>
        </h6>
        
        <ul class="nav flex-column mb-2">
            <li class="nav-item">
                <a class="nav-link <?php echo $currentPage === 'api-tokens' ? 'active' : ''; ?>" href="api-tokens.php">
                    <i class="fas fa-key"></i>
                    API Tokens
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link <?php echo $currentPage === 'app-settings' ? 'active' : ''; ?>" href="app-settings.php">
                    <i class="fas fa-mobile-alt"></i>
                    App Settings
                </a>
            </li>
        </ul>
        
        <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
            <span>Website</span>
        </h6>
        
        <ul class="nav flex-column mb-2">
            <li class="nav-item">
                <a class="nav-link" href="/" target="_blank">
                    <i class="fas fa-external-link-alt"></i>
                    View Website
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link <?php echo $currentPage === 'analytics' ? 'active' : ''; ?>" href="analytics.php">
                    <i class="fas fa-chart-bar"></i>
                    Analytics
                </a>
            </li>
        </ul>
        
        <!-- Quick Stats -->
        <div class="px-3 mt-4">
            <div class="card bg-secondary text-white">
                <div class="card-body p-3">
                    <h6 class="card-title mb-2">
                        <i class="fas fa-info-circle me-1"></i>
                        Quick Stats
                    </h6>
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="h6 mb-0"><?php echo fetchOne("SELECT COUNT(*) as count FROM projects")['count']; ?></div>
                            <small>Projects</small>
                        </div>
                        <div class="col-6">
                            <div class="h6 mb-0"><?php echo fetchOne("SELECT COUNT(*) as count FROM messages WHERE status = 'new'")['count']; ?></div>
                            <small>New Messages</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- System Info -->
        <div class="px-3 mt-3">
            <div class="card bg-dark border-secondary">
                <div class="card-body p-3">
                    <h6 class="card-title mb-2 text-light">
                        <i class="fas fa-server me-1"></i>
                        System Info
                    </h6>
                    <small class="text-muted d-block">PHP: <?php echo PHP_VERSION; ?></small>
                    <small class="text-muted d-block">MySQL: <?php echo fetchOne("SELECT VERSION() as version")['version']; ?></small>
                    <small class="text-muted d-block">Disk: <?php echo formatFileSize(disk_free_space('.')); ?> free</small>
                </div>
            </div>
        </div>
    </div>
</nav>

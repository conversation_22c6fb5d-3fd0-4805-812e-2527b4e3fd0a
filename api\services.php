<?php
/**
 * Services API for Mobile App
 * Handles CRUD operations for services
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

require_once __DIR__ . '/../includes/database.php';
require_once __DIR__ . '/../includes/functions.php';

// Authenticate user for write operations
$method = $_SERVER['REQUEST_METHOD'];
if (in_array($method, ['POST', 'PUT', 'DELETE'])) {
    $authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? '';
    $tokenData = validateApiToken($authHeader);
    
    if (!$tokenData) {
        sendErrorResponse('Unauthorized', 401);
    }
}

try {
    switch ($method) {
        case 'GET':
            handleGetServices();
            break;
            
        case 'POST':
            handleCreateService();
            break;
            
        case 'PUT':
            handleUpdateService();
            break;
            
        case 'DELETE':
            handleDeleteService();
            break;
            
        default:
            sendErrorResponse('Method not allowed', 405);
    }
} catch (Exception $e) {
    error_log("Services API Error: " . $e->getMessage());
    sendErrorResponse('Internal server error', 500);
}

/**
 * Get services with optional filtering
 */
function handleGetServices() {
    $serviceId = $_GET['id'] ?? '';
    $active = $_GET['active'] ?? '';
    $search = $_GET['search'] ?? '';
    
    // If specific service ID is requested
    if (!empty($serviceId)) {
        $service = fetchOne("SELECT * FROM services WHERE id = ?", [$serviceId]);
        
        if (!$service) {
            sendErrorResponse('Service not found', 404);
        }
        
        // Decode features JSON
        if ($service['features']) {
            $service['features'] = json_decode($service['features'], true);
        }
        
        sendSuccessResponse($service);
    }
    
    // Build query conditions
    $conditions = [];
    $params = [];
    
    if (!empty($active)) {
        $conditions[] = "is_active = ?";
        $params[] = $active === 'true' ? 1 : 0;
    }
    
    if (!empty($search)) {
        $conditions[] = "(name LIKE ? OR description LIKE ? OR short_description LIKE ?)";
        $searchTerm = "%{$search}%";
        $params[] = $searchTerm;
        $params[] = $searchTerm;
        $params[] = $searchTerm;
    }
    
    $whereClause = !empty($conditions) ? 'WHERE ' . implode(' AND ', $conditions) : '';
    
    $sql = "SELECT * FROM services {$whereClause} ORDER BY sort_order ASC, name ASC";
    
    $services = fetchAll($sql, $params);
    
    // Decode features JSON for each service
    foreach ($services as &$service) {
        if ($service['features']) {
            $service['features'] = json_decode($service['features'], true);
        }
    }
    
    sendSuccessResponse($services);
}

/**
 * Create new service
 */
function handleCreateService() {
    $input = json_decode(file_get_contents('php://input'), true);
    
    // Validate required fields
    $required = ['name', 'description'];
    foreach ($required as $field) {
        if (empty($input[$field])) {
            sendErrorResponse("Field '{$field}' is required", 400);
        }
    }
    
    // Generate slug from name if not provided
    $slug = $input['slug'] ?? generateSlug($input['name']);
    
    // Check if slug already exists
    $existingService = fetchOne("SELECT id FROM services WHERE slug = ?", [$slug]);
    if ($existingService) {
        sendErrorResponse('Service with this slug already exists', 400);
    }
    
    // Sanitize input
    $data = [
        'name' => sanitizeInput($input['name']),
        'slug' => $slug,
        'description' => sanitizeInput($input['description']),
        'short_description' => sanitizeInput($input['short_description'] ?? ''),
        'icon' => sanitizeInput($input['icon'] ?? ''),
        'image' => sanitizeInput($input['image'] ?? ''),
        'features' => isset($input['features']) ? json_encode($input['features']) : null,
        'is_active' => isset($input['is_active']) ? ($input['is_active'] ? 1 : 0) : 1,
        'sort_order' => intval($input['sort_order'] ?? 0)
    ];
    
    try {
        $serviceId = insertData('services', $data);
        sendSuccessResponse(['id' => $serviceId], 'Service created successfully');
        
    } catch (Exception $e) {
        error_log("Create service error: " . $e->getMessage());
        sendErrorResponse('Failed to create service', 500);
    }
}

/**
 * Update existing service
 */
function handleUpdateService() {
    $serviceId = $_GET['id'] ?? '';
    
    if (empty($serviceId)) {
        sendErrorResponse('Service ID is required', 400);
    }
    
    // Check if service exists
    $existingService = fetchOne("SELECT * FROM services WHERE id = ?", [$serviceId]);
    if (!$existingService) {
        sendErrorResponse('Service not found', 404);
    }
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    // Build update data (only include provided fields)
    $data = [];
    $allowedFields = [
        'name', 'slug', 'description', 'short_description', 'icon', 
        'image', 'features', 'is_active', 'sort_order'
    ];
    
    foreach ($allowedFields as $field) {
        if (isset($input[$field])) {
            if (in_array($field, ['name', 'slug', 'description', 'short_description', 'icon', 'image'])) {
                $data[$field] = sanitizeInput($input[$field]);
            } elseif ($field === 'features') {
                $data[$field] = json_encode($input[$field]);
            } elseif ($field === 'is_active') {
                $data[$field] = $input[$field] ? 1 : 0;
            } elseif ($field === 'sort_order') {
                $data[$field] = intval($input[$field]);
            }
        }
    }
    
    // Check slug uniqueness if slug is being updated
    if (isset($data['slug']) && $data['slug'] !== $existingService['slug']) {
        $slugExists = fetchOne("SELECT id FROM services WHERE slug = ? AND id != ?", [$data['slug'], $serviceId]);
        if ($slugExists) {
            sendErrorResponse('Service with this slug already exists', 400);
        }
    }
    
    if (empty($data)) {
        sendErrorResponse('No valid fields to update', 400);
    }
    
    try {
        updateData('services', $data, 'id = ?', [$serviceId]);
        sendSuccessResponse([], 'Service updated successfully');
        
    } catch (Exception $e) {
        error_log("Update service error: " . $e->getMessage());
        sendErrorResponse('Failed to update service', 500);
    }
}

/**
 * Delete service
 */
function handleDeleteService() {
    $serviceId = $_GET['id'] ?? '';
    
    if (empty($serviceId)) {
        sendErrorResponse('Service ID is required', 400);
    }
    
    // Check if service exists
    $existingService = fetchOne("SELECT * FROM services WHERE id = ?", [$serviceId]);
    if (!$existingService) {
        sendErrorResponse('Service not found', 404);
    }
    
    try {
        deleteData('services', 'id = ?', [$serviceId]);
        sendSuccessResponse([], 'Service deleted successfully');
        
    } catch (Exception $e) {
        error_log("Delete service error: " . $e->getMessage());
        sendErrorResponse('Failed to delete service', 500);
    }
}

/**
 * Generate slug from text
 */
function generateSlug($text) {
    // Convert to lowercase
    $slug = strtolower($text);
    
    // Replace spaces and special characters with hyphens
    $slug = preg_replace('/[^a-z0-9]+/', '-', $slug);
    
    // Remove leading/trailing hyphens
    $slug = trim($slug, '-');
    
    return $slug;
}

/**
 * Reorder services
 */
if ($_SERVER['REQUEST_METHOD'] === 'PUT' && isset($_GET['action']) && $_GET['action'] === 'reorder') {
    $authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? '';
    $tokenData = validateApiToken($authHeader);
    
    if (!$tokenData) {
        sendErrorResponse('Unauthorized', 401);
    }
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (empty($input['services']) || !is_array($input['services'])) {
        sendErrorResponse('Services array is required', 400);
    }
    
    try {
        foreach ($input['services'] as $index => $serviceData) {
            if (isset($serviceData['id'])) {
                updateData('services', ['sort_order' => $index], 'id = ?', [$serviceData['id']]);
            }
        }
        
        sendSuccessResponse([], 'Services reordered successfully');
        
    } catch (Exception $e) {
        error_log("Reorder services error: " . $e->getMessage());
        sendErrorResponse('Failed to reorder services', 500);
    }
}

/**
 * Toggle service active status
 */
if ($_SERVER['REQUEST_METHOD'] === 'PUT' && isset($_GET['action']) && $_GET['action'] === 'toggle-status') {
    $authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? '';
    $tokenData = validateApiToken($authHeader);
    
    if (!$tokenData) {
        sendErrorResponse('Unauthorized', 401);
    }
    
    $serviceId = $_GET['id'] ?? '';
    
    if (empty($serviceId)) {
        sendErrorResponse('Service ID is required', 400);
    }
    
    // Check if service exists
    $existingService = fetchOne("SELECT * FROM services WHERE id = ?", [$serviceId]);
    if (!$existingService) {
        sendErrorResponse('Service not found', 404);
    }
    
    try {
        $newStatus = $existingService['is_active'] ? 0 : 1;
        updateData('services', ['is_active' => $newStatus], 'id = ?', [$serviceId]);
        
        $statusText = $newStatus ? 'activated' : 'deactivated';
        sendSuccessResponse(['is_active' => $newStatus], "Service {$statusText} successfully");
        
    } catch (Exception $e) {
        error_log("Toggle service status error: " . $e->getMessage());
        sendErrorResponse('Failed to toggle service status', 500);
    }
}

/**
 * Get service statistics
 */
if ($_SERVER['REQUEST_METHOD'] === 'GET' && isset($_GET['action']) && $_GET['action'] === 'stats') {
    try {
        $stats = [
            'total_services' => fetchOne("SELECT COUNT(*) as count FROM services")['count'],
            'active_services' => fetchOne("SELECT COUNT(*) as count FROM services WHERE is_active = 1")['count'],
            'inactive_services' => fetchOne("SELECT COUNT(*) as count FROM services WHERE is_active = 0")['count'],
            'services_with_images' => fetchOne("SELECT COUNT(*) as count FROM services WHERE image IS NOT NULL AND image != ''")['count'],
            'recent_services' => fetchAll("SELECT id, name, is_active, created_at FROM services ORDER BY created_at DESC LIMIT 5")
        ];
        
        sendSuccessResponse($stats);
        
    } catch (Exception $e) {
        error_log("Service stats error: " . $e->getMessage());
        sendErrorResponse('Failed to get service statistics', 500);
    }
}
?>

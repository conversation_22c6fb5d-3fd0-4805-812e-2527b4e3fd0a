<?php
require_once __DIR__ . '/database.php';
require_once __DIR__ . '/functions.php';

$settings = getSettings();
$currentPage = basename($_SERVER['PHP_SELF'], '.php');

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($pageTitle) ? $pageTitle . ' - ' : ''; ?><?php echo getSetting('site_title', 'Flori Construction Ltd'); ?></title>
    <meta name="description" content="<?php echo isset($pageDescription) ? $pageDescription : getSetting('site_description'); ?>">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Oswald:wght@400;500;600&display=swap" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="/assets/css/style.css">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/assets/images/favicon.ico">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="<?php echo isset($pageTitle) ? $pageTitle : getSetting('site_title'); ?>">
    <meta property="og:description" content="<?php echo isset($pageDescription) ? $pageDescription : getSetting('site_description'); ?>">
    <meta property="og:type" content="website">
    <meta property="og:url" content="<?php echo 'https://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI']; ?>">
    <meta property="og:image" content="/assets/images/og-image.jpg">
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="<?php echo isset($pageTitle) ? $pageTitle : getSetting('site_title'); ?>">
    <meta name="twitter:description" content="<?php echo isset($pageDescription) ? $pageDescription : getSetting('site_description'); ?>">
    <meta name="twitter:image" content="/assets/images/og-image.jpg">
</head>
<body>
    <!-- Top Bar -->
    <div class="top-bar bg-dark text-white py-2 d-none d-md-block">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <div class="contact-info">
                        <span class="me-3">
                            <i class="fas fa-phone me-1"></i>
                            <a href="tel:<?php echo getSetting('company_phone'); ?>" class="text-white text-decoration-none">
                                <?php echo getSetting('company_phone'); ?>
                            </a>
                        </span>
                        <span>
                            <i class="fas fa-envelope me-1"></i>
                            <a href="mailto:<?php echo getSetting('company_email'); ?>" class="text-white text-decoration-none">
                                <?php echo getSetting('company_email'); ?>
                            </a>
                        </span>
                    </div>
                </div>
                <div class="col-md-6 text-end">
                    <div class="social-links">
                        <?php if (getSetting('facebook_url')): ?>
                        <a href="<?php echo getSetting('facebook_url'); ?>" target="_blank" class="text-white me-2">
                            <i class="fab fa-facebook-f"></i>
                        </a>
                        <?php endif; ?>
                        
                        <?php if (getSetting('instagram_url')): ?>
                        <a href="<?php echo getSetting('instagram_url'); ?>" target="_blank" class="text-white me-2">
                            <i class="fab fa-instagram"></i>
                        </a>
                        <?php endif; ?>
                        
                        <?php if (getSetting('youtube_url')): ?>
                        <a href="<?php echo getSetting('youtube_url'); ?>" target="_blank" class="text-white me-2">
                            <i class="fab fa-youtube"></i>
                        </a>
                        <?php endif; ?>
                        
                        <?php if (getSetting('linkedin_url')): ?>
                        <a href="<?php echo getSetting('linkedin_url'); ?>" target="_blank" class="text-white">
                            <i class="fab fa-linkedin-in"></i>
                        </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm sticky-top">
        <div class="container">
            <!-- Logo -->
            <a class="navbar-brand" href="/">
                <img src="/assets/images/logo.png" alt="<?php echo getSetting('company_name'); ?>" height="50">
            </a>

            <!-- Mobile Toggle -->
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <!-- Navigation Menu -->
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link <?php echo $currentPage === 'index' ? 'active' : ''; ?>" href="/">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo $currentPage === 'about' ? 'active' : ''; ?>" href="/about.php">About Us</a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle <?php echo $currentPage === 'projects' ? 'active' : ''; ?>" href="#" id="projectsDropdown" role="button" data-bs-toggle="dropdown">
                            Our Projects
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/projects.php">All Projects</a></li>
                            <li><a class="dropdown-item" href="/projects.php?category=completed">Completed Projects</a></li>
                            <li><a class="dropdown-item" href="/projects.php?category=ongoing">Ongoing Projects</a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo $currentPage === 'services' ? 'active' : ''; ?>" href="/services.php">Our Services</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo $currentPage === 'media' ? 'active' : ''; ?>" href="/media.php">Media</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo $currentPage === 'contact' ? 'active' : ''; ?>" href="/contact.php">Contact Us</a>
                    </li>
                </ul>
                
                <!-- Call to Action Button -->
                <div class="ms-3">
                    <a href="/contact.php" class="btn btn-primary">
                        <i class="fas fa-phone me-1"></i>
                        Get Quote
                    </a>
                </div>
            </div>
        </div>
    </nav>

<?php
/**
 * Authentication API for Mobile App
 * Handles login, logout, and token validation
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

require_once __DIR__ . '/../includes/database.php';
require_once __DIR__ . '/../includes/functions.php';

$method = $_SERVER['REQUEST_METHOD'];
$input = json_decode(file_get_contents('php://input'), true);

try {
    switch ($method) {
        case 'POST':
            handleLogin($input);
            break;
            
        case 'GET':
            handleTokenValidation();
            break;
            
        default:
            sendErrorResponse('Method not allowed', 405);
    }
} catch (Exception $e) {
    error_log("Auth API Error: " . $e->getMessage());
    sendErrorResponse('Internal server error', 500);
}

/**
 * Handle user login
 */
function handleLogin($input) {
    // Validate input
    if (empty($input['username']) || empty($input['password'])) {
        sendErrorResponse('Username and password are required', 400);
    }
    
    $username = sanitizeInput($input['username']);
    $password = $input['password'];
    
    // Rate limiting - check for too many login attempts
    $ip = $_SERVER['REMOTE_ADDR'];
    $recentAttempts = fetchOne(
        "SELECT COUNT(*) as attempts FROM login_attempts 
         WHERE ip_address = ? AND attempted_at > DATE_SUB(NOW(), INTERVAL 15 MINUTE)",
        [$ip]
    );
    
    if ($recentAttempts && $recentAttempts['attempts'] >= 5) {
        sendErrorResponse('Too many login attempts. Please try again later.', 429);
    }
    
    // Find user
    $user = fetchOne(
        "SELECT * FROM users WHERE (username = ? OR email = ?) AND is_active = 1",
        [$username, $username]
    );
    
    if (!$user || !password_verify($password, $user['password'])) {
        // Log failed attempt
        logLoginAttempt($ip, $username, false);
        sendErrorResponse('Invalid credentials', 401);
    }
    
    // Generate API token
    $token = generateApiToken($user['id']);
    
    // Update last login
    updateData('users', ['last_login' => date('Y-m-d H:i:s')], 'id = ?', [$user['id']]);
    
    // Log successful attempt
    logLoginAttempt($ip, $username, true);
    
    // Return user data and token
    sendSuccessResponse([
        'user' => [
            'id' => $user['id'],
            'username' => $user['username'],
            'email' => $user['email'],
            'role' => $user['role']
        ],
        'token' => $token,
        'expires_at' => date('Y-m-d H:i:s', strtotime('+30 days'))
    ], 'Login successful');
}

/**
 * Handle token validation
 */
function handleTokenValidation() {
    $authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? '';
    
    if (empty($authHeader)) {
        sendErrorResponse('Authorization header required', 401);
    }
    
    $tokenData = validateApiToken($authHeader);
    
    if (!$tokenData) {
        sendErrorResponse('Invalid or expired token', 401);
    }
    
    sendSuccessResponse([
        'user' => [
            'id' => $tokenData['user_id'],
            'username' => $tokenData['username'],
            'email' => $tokenData['email'],
            'role' => $tokenData['role']
        ],
        'token_expires_at' => $tokenData['expires_at']
    ], 'Token is valid');
}

/**
 * Log login attempt
 */
function logLoginAttempt($ip, $username, $success) {
    try {
        // Create login_attempts table if it doesn't exist
        executeQuery("
            CREATE TABLE IF NOT EXISTS login_attempts (
                id INT AUTO_INCREMENT PRIMARY KEY,
                ip_address VARCHAR(45) NOT NULL,
                username VARCHAR(255),
                success BOOLEAN DEFAULT FALSE,
                attempted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                user_agent TEXT
            )
        ");
        
        insertData('login_attempts', [
            'ip_address' => $ip,
            'username' => $username,
            'success' => $success ? 1 : 0,
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
        ]);
    } catch (Exception $e) {
        error_log("Failed to log login attempt: " . $e->getMessage());
    }
}

/**
 * Logout endpoint (revoke token)
 */
if ($_SERVER['REQUEST_METHOD'] === 'DELETE') {
    $authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? '';
    
    if (empty($authHeader)) {
        sendErrorResponse('Authorization header required', 401);
    }
    
    $token = str_replace('Bearer ', '', $authHeader);
    
    try {
        deleteData('api_tokens', 'token = ?', [$token]);
        sendSuccessResponse([], 'Logout successful');
    } catch (Exception $e) {
        sendErrorResponse('Failed to logout', 500);
    }
}

/**
 * Refresh token endpoint
 */
if ($_SERVER['REQUEST_METHOD'] === 'PUT') {
    $authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? '';
    
    if (empty($authHeader)) {
        sendErrorResponse('Authorization header required', 401);
    }
    
    $tokenData = validateApiToken($authHeader);
    
    if (!$tokenData) {
        sendErrorResponse('Invalid or expired token', 401);
    }
    
    // Generate new token
    $newToken = generateApiToken($tokenData['user_id']);
    
    sendSuccessResponse([
        'token' => $newToken,
        'expires_at' => date('Y-m-d H:i:s', strtotime('+30 days'))
    ], 'Token refreshed successfully');
}

/**
 * Password reset request
 */
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_GET['action']) && $_GET['action'] === 'reset-password') {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (empty($input['email'])) {
        sendErrorResponse('Email is required', 400);
    }
    
    $email = sanitizeInput($input['email']);
    
    if (!isValidEmail($email)) {
        sendErrorResponse('Invalid email format', 400);
    }
    
    $user = fetchOne("SELECT * FROM users WHERE email = ? AND is_active = 1", [$email]);
    
    if (!$user) {
        // Don't reveal if email exists or not
        sendSuccessResponse([], 'If the email exists, a reset link has been sent');
    }
    
    // Generate reset token
    $resetToken = generateToken(32);
    $expiresAt = date('Y-m-d H:i:s', strtotime('+1 hour'));
    
    // Store reset token
    try {
        executeQuery("
            CREATE TABLE IF NOT EXISTS password_resets (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                token VARCHAR(255) NOT NULL,
                expires_at DATETIME NOT NULL,
                used BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
            )
        ");
        
        insertData('password_resets', [
            'user_id' => $user['id'],
            'token' => $resetToken,
            'expires_at' => $expiresAt
        ]);
        
        // Here you would send an email with the reset link
        // For now, we'll just return success
        sendSuccessResponse([], 'Password reset link sent to your email');
        
    } catch (Exception $e) {
        error_log("Password reset error: " . $e->getMessage());
        sendErrorResponse('Failed to process password reset', 500);
    }
}

/**
 * Change password
 */
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_GET['action']) && $_GET['action'] === 'change-password') {
    $authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? '';
    
    if (empty($authHeader)) {
        sendErrorResponse('Authorization header required', 401);
    }
    
    $tokenData = validateApiToken($authHeader);
    
    if (!$tokenData) {
        sendErrorResponse('Invalid or expired token', 401);
    }
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (empty($input['current_password']) || empty($input['new_password'])) {
        sendErrorResponse('Current password and new password are required', 400);
    }
    
    $currentPassword = $input['current_password'];
    $newPassword = $input['new_password'];
    
    // Validate new password strength
    if (strlen($newPassword) < 8) {
        sendErrorResponse('New password must be at least 8 characters long', 400);
    }
    
    // Get current user
    $user = fetchOne("SELECT * FROM users WHERE id = ?", [$tokenData['user_id']]);
    
    if (!password_verify($currentPassword, $user['password'])) {
        sendErrorResponse('Current password is incorrect', 400);
    }
    
    // Update password
    $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
    
    try {
        updateData('users', ['password' => $hashedPassword], 'id = ?', [$user['id']]);
        
        // Revoke all existing tokens for security
        deleteData('api_tokens', 'user_id = ?', [$user['id']]);
        
        sendSuccessResponse([], 'Password changed successfully. Please login again.');
        
    } catch (Exception $e) {
        error_log("Password change error: " . $e->getMessage());
        sendErrorResponse('Failed to change password', 500);
    }
}
?>

/* Flori Construction Website Styles */

/* CSS Variables */
:root {
    --primary-color: #ff6b35;
    --secondary-color: #2c3e50;
    --accent-color: #f39c12;
    --dark-color: #1a1a1a;
    --light-color: #f8f9fa;
    --text-color: #333;
    --text-muted: #6c757d;
    --border-color: #dee2e6;
    --shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);
}

/* Global Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Roboto', sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    overflow-x: hidden;
}

h1, h2, h3, h4, h5, h6 {
    font-family: 'Oswald', sans-serif;
    font-weight: 600;
    line-height: 1.2;
}

.section-title {
    position: relative;
    display: inline-block;
    font-size: 2.5rem;
    font-weight: 600;
    color: var(--secondary-color);
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: var(--primary-color);
}

/* Bootstrap Customizations */
.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    font-weight: 500;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background-color: #e55a2b;
    border-color: #e55a2b;
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
    font-weight: 500;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    transform: translateY(-2px);
}

/* Navigation Styles */
.top-bar {
    font-size: 0.875rem;
}

.navbar {
    padding: 1rem 0;
    transition: all 0.3s ease;
}

.navbar.scrolled {
    padding: 0.5rem 0;
    background-color: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(10px);
}

.navbar-brand img {
    transition: all 0.3s ease;
}

.navbar-nav .nav-link {
    font-weight: 500;
    color: var(--text-color) !important;
    margin: 0 0.5rem;
    padding: 0.5rem 1rem !important;
    border-radius: 0.25rem;
    transition: all 0.3s ease;
}

.navbar-nav .nav-link:hover,
.navbar-nav .nav-link.active {
    color: var(--primary-color) !important;
    background-color: rgba(255, 107, 53, 0.1);
}

.dropdown-menu {
    border: none;
    box-shadow: var(--shadow-lg);
    border-radius: 0.5rem;
    padding: 0.5rem 0;
}

.dropdown-item {
    padding: 0.5rem 1.5rem;
    transition: all 0.3s ease;
}

.dropdown-item:hover {
    background-color: var(--primary-color);
    color: white;
}

/* Hero Section */
.hero-section {
    height: 100vh;
    min-height: 600px;
    display: flex;
    align-items: center;
    position: relative;
}

.hero-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -2;
}

.hero-bg img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.hero-overlay {
    background: linear-gradient(135deg, rgba(44, 62, 80, 0.8), rgba(255, 107, 53, 0.6));
    z-index: -1;
}

.hero-content {
    z-index: 1;
}

.hero-content h1 {
    font-size: 3.5rem;
    margin-bottom: 1.5rem;
}

.hero-content .lead {
    font-size: 1.25rem;
    margin-bottom: 2rem;
}

.scroll-indicator {
    z-index: 1;
}

.scroll-arrow {
    font-size: 1.5rem;
    opacity: 0.8;
}

/* Animations */
@keyframes fadeUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

.animate-fade-up {
    animation: fadeUp 0.8s ease-out forwards;
    opacity: 0;
}

.animate-bounce {
    animation: bounce 2s infinite;
}

/* Stats Section */
.stats-section {
    background: linear-gradient(135deg, var(--primary-color), #e55a2b);
}

.stat-item {
    padding: 2rem 1rem;
    transition: transform 0.3s ease;
}

.stat-item:hover {
    transform: translateY(-5px);
}

.stat-number {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 1.1rem;
    opacity: 0.9;
}

/* Service Cards */
.service-card {
    transition: all 0.3s ease;
    border: 1px solid var(--border-color);
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-color);
}

.service-icon {
    margin-bottom: 1.5rem;
}

.service-title {
    color: var(--secondary-color);
    margin-bottom: 1rem;
}

/* Project Cards */
.project-card {
    position: relative;
    overflow: hidden;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
}

.project-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.project-image {
    position: relative;
    overflow: hidden;
}

.project-image img {
    transition: transform 0.3s ease;
}

.project-card:hover .project-image img {
    transform: scale(1.1);
}

.project-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(44, 62, 80, 0.8), rgba(255, 107, 53, 0.8));
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.project-card:hover .project-overlay {
    opacity: 1;
}

.project-info {
    padding: 2rem;
    text-align: center;
}

.project-title {
    font-size: 1.25rem;
    margin-bottom: 0.5rem;
}

.project-type {
    font-size: 0.9rem;
    opacity: 0.8;
    margin-bottom: 1rem;
}

/* CTA Section */
.cta-section {
    background: linear-gradient(135deg, var(--secondary-color), var(--primary-color));
}

/* Footer */
footer {
    background-color: var(--dark-color) !important;
}

.footer-section h5 {
    color: var(--primary-color);
    margin-bottom: 1.5rem;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section a {
    transition: all 0.3s ease;
}

.footer-section a:hover {
    color: var(--primary-color) !important;
    padding-left: 5px;
}

.social-links a {
    display: inline-block;
    width: 40px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.social-links a:hover {
    background-color: var(--primary-color);
    transform: translateY(-3px);
}

/* Back to Top Button */
#backToTop {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    border: none;
    background-color: var(--primary-color);
    color: white;
    font-size: 1.2rem;
    transition: all 0.3s ease;
    z-index: 1000;
}

#backToTop:hover {
    background-color: #e55a2b;
    transform: translateY(-3px);
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-content h1 {
        font-size: 2.5rem;
    }
    
    .hero-content .lead {
        font-size: 1.1rem;
    }
    
    .section-title {
        font-size: 2rem;
    }
    
    .stat-number {
        font-size: 2.5rem;
    }
    
    .hero-buttons .btn {
        display: block;
        width: 100%;
        margin-bottom: 1rem;
    }
    
    .hero-buttons .btn:last-child {
        margin-bottom: 0;
    }
}

@media (max-width: 576px) {
    .hero-content h1 {
        font-size: 2rem;
    }
    
    .section-title {
        font-size: 1.75rem;
    }
    
    .stat-number {
        font-size: 2rem;
    }
}

/* Utility Classes */
.object-fit-cover {
    object-fit: cover;
}

.text-primary {
    color: var(--primary-color) !important;
}

.bg-primary {
    background-color: var(--primary-color) !important;
}

.border-primary {
    border-color: var(--primary-color) !important;
}

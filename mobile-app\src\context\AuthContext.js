import React, { createContext, useContext, useReducer, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { showMessage } from 'react-native-flash-message';
import { apiService } from '../services/apiService';

// Initial state
const initialState = {
  user: null,
  token: null,
  isAuthenticated: false,
  isLoading: true,
  error: null,
};

// Action types
const AUTH_ACTIONS = {
  LOGIN_START: 'LOGIN_START',
  LOGIN_SUCCESS: 'LOGIN_SUCCESS',
  LOGIN_FAILURE: 'LOGIN_FAILURE',
  LOGOUT: 'LOGOUT',
  RESTORE_TOKEN: 'RESTORE_TOKEN',
  CLEAR_ERROR: 'CLEAR_ERROR',
  UPDATE_USER: 'UPDATE_USER',
};

// Reducer
function authReducer(state, action) {
  switch (action.type) {
    case AUTH_ACTIONS.LOGIN_START:
      return {
        ...state,
        isLoading: true,
        error: null,
      };
    case AUTH_ACTIONS.LOGIN_SUCCESS:
      return {
        ...state,
        user: action.payload.user,
        token: action.payload.token,
        isAuthenticated: true,
        isLoading: false,
        error: null,
      };
    case AUTH_ACTIONS.LOGIN_FAILURE:
      return {
        ...state,
        user: null,
        token: null,
        isAuthenticated: false,
        isLoading: false,
        error: action.payload,
      };
    case AUTH_ACTIONS.LOGOUT:
      return {
        ...state,
        user: null,
        token: null,
        isAuthenticated: false,
        isLoading: false,
        error: null,
      };
    case AUTH_ACTIONS.RESTORE_TOKEN:
      return {
        ...state,
        user: action.payload.user,
        token: action.payload.token,
        isAuthenticated: !!action.payload.token,
        isLoading: false,
      };
    case AUTH_ACTIONS.CLEAR_ERROR:
      return {
        ...state,
        error: null,
      };
    case AUTH_ACTIONS.UPDATE_USER:
      return {
        ...state,
        user: { ...state.user, ...action.payload },
      };
    default:
      return state;
  }
}

// Create context
const AuthContext = createContext();

// Auth provider component
export function AuthProvider({ children }) {
  const [state, dispatch] = useReducer(authReducer, initialState);

  // Restore token on app start
  useEffect(() => {
    restoreToken();
  }, []);

  // Restore token from storage
  const restoreToken = async () => {
    try {
      const token = await AsyncStorage.getItem('authToken');
      const userData = await AsyncStorage.getItem('userData');
      
      if (token && userData) {
        const user = JSON.parse(userData);
        
        // Validate token with server
        try {
          const response = await apiService.validateToken(token);
          if (response.success) {
            dispatch({
              type: AUTH_ACTIONS.RESTORE_TOKEN,
              payload: { user, token },
            });
            return;
          }
        } catch (error) {
          // Token is invalid, remove it
          await AsyncStorage.multiRemove(['authToken', 'userData']);
        }
      }
      
      dispatch({
        type: AUTH_ACTIONS.RESTORE_TOKEN,
        payload: { user: null, token: null },
      });
    } catch (error) {
      console.error('Error restoring token:', error);
      dispatch({
        type: AUTH_ACTIONS.RESTORE_TOKEN,
        payload: { user: null, token: null },
      });
    }
  };

  // Login function
  const login = async (username, password) => {
    dispatch({ type: AUTH_ACTIONS.LOGIN_START });

    try {
      const response = await apiService.login(username, password);
      
      if (response.success) {
        const { user, token } = response.data;
        
        // Store token and user data
        await AsyncStorage.setItem('authToken', token);
        await AsyncStorage.setItem('userData', JSON.stringify(user));
        
        dispatch({
          type: AUTH_ACTIONS.LOGIN_SUCCESS,
          payload: { user, token },
        });
        
        showMessage({
          message: 'Login Successful',
          description: `Welcome back, ${user.username}!`,
          type: 'success',
        });
        
        return { success: true };
      } else {
        dispatch({
          type: AUTH_ACTIONS.LOGIN_FAILURE,
          payload: response.message || 'Login failed',
        });
        
        showMessage({
          message: 'Login Failed',
          description: response.message || 'Invalid credentials',
          type: 'danger',
        });
        
        return { success: false, message: response.message };
      }
    } catch (error) {
      const errorMessage = error.response?.data?.message || 'Network error';
      
      dispatch({
        type: AUTH_ACTIONS.LOGIN_FAILURE,
        payload: errorMessage,
      });
      
      showMessage({
        message: 'Login Error',
        description: errorMessage,
        type: 'danger',
      });
      
      return { success: false, message: errorMessage };
    }
  };

  // Logout function
  const logout = async () => {
    try {
      // Call logout API if token exists
      if (state.token) {
        await apiService.logout(state.token);
      }
    } catch (error) {
      console.error('Logout API error:', error);
    } finally {
      // Clear local storage
      await AsyncStorage.multiRemove(['authToken', 'userData']);
      
      dispatch({ type: AUTH_ACTIONS.LOGOUT });
      
      showMessage({
        message: 'Logged Out',
        description: 'You have been successfully logged out',
        type: 'info',
      });
    }
  };

  // Update user profile
  const updateUser = (userData) => {
    dispatch({
      type: AUTH_ACTIONS.UPDATE_USER,
      payload: userData,
    });
  };

  // Clear error
  const clearError = () => {
    dispatch({ type: AUTH_ACTIONS.CLEAR_ERROR });
  };

  // Change password
  const changePassword = async (currentPassword, newPassword) => {
    try {
      const response = await apiService.changePassword(
        state.token,
        currentPassword,
        newPassword
      );
      
      if (response.success) {
        showMessage({
          message: 'Password Changed',
          description: 'Your password has been updated successfully',
          type: 'success',
        });
        
        // Force logout to re-authenticate
        await logout();
        
        return { success: true };
      } else {
        showMessage({
          message: 'Password Change Failed',
          description: response.message || 'Failed to change password',
          type: 'danger',
        });
        
        return { success: false, message: response.message };
      }
    } catch (error) {
      const errorMessage = error.response?.data?.message || 'Network error';
      
      showMessage({
        message: 'Password Change Error',
        description: errorMessage,
        type: 'danger',
      });
      
      return { success: false, message: errorMessage };
    }
  };

  // Context value
  const value = {
    ...state,
    login,
    logout,
    updateUser,
    clearError,
    changePassword,
    restoreToken,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

// Custom hook to use auth context
export function useAuth() {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

-- <PERSON>lori Construction Website Database Schema
-- Create database
CREATE DATABASE IF NOT EXISTS flori_construction;
USE flori_construction;

-- Users table for admin authentication
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    role ENUM('admin', 'manager') DEFAULT 'admin',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login DATETIME NULL,
    is_active BOOLEAN DEFAULT TRUE
);

-- Projects table
CREATE TABLE projects (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    short_description VARCHAR(500),
    category ENUM('completed', 'ongoing') DEFAULT 'completed',
    project_type VARCHAR(100), -- Civil Engineering, Groundworks, etc.
    location VARCHAR(255),
    client_name VARCHA<PERSON>(255),
    start_date DATE,
    end_date DATE,
    featured BOOLEAN DEFAULT FALSE,
    status ENUM('planning', 'in_progress', 'completed', 'on_hold') DEFAULT 'completed',
    budget DECIMAL(15,2) NULL,
    image_main VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Services table
CREATE TABLE services (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    description TEXT,
    short_description VARCHAR(500),
    icon VARCHAR(100), -- FontAwesome icon class
    image VARCHAR(255),
    features JSON, -- Store service features as JSON
    is_active BOOLEAN DEFAULT TRUE,
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Media table for gallery
CREATE TABLE media (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255),
    description TEXT,
    file_path VARCHAR(255) NOT NULL,
    file_type ENUM('image', 'video') DEFAULT 'image',
    file_size INT, -- in bytes
    mime_type VARCHAR(100),
    category VARCHAR(100), -- project, service, general
    related_id INT NULL, -- ID of related project or service
    alt_text VARCHAR(255),
    is_featured BOOLEAN DEFAULT FALSE,
    uploaded_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (uploaded_by) REFERENCES users(id) ON DELETE SET NULL
);

-- Messages from contact form
CREATE TABLE messages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    phone VARCHAR(20),
    subject VARCHAR(255),
    message TEXT NOT NULL,
    status ENUM('new', 'read', 'replied', 'archived') DEFAULT 'new',
    ip_address VARCHAR(45),
    user_agent TEXT,
    replied_at DATETIME NULL,
    replied_by INT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (replied_by) REFERENCES users(id) ON DELETE SET NULL
);

-- Company settings
CREATE TABLE settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    setting_type ENUM('text', 'textarea', 'json', 'boolean', 'number') DEFAULT 'text',
    description VARCHAR(255),
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Project images (multiple images per project)
CREATE TABLE project_images (
    id INT AUTO_INCREMENT PRIMARY KEY,
    project_id INT NOT NULL,
    image_path VARCHAR(255) NOT NULL,
    caption VARCHAR(255),
    is_main BOOLEAN DEFAULT FALSE,
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE
);

-- API tokens for mobile app authentication
CREATE TABLE api_tokens (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    token VARCHAR(255) UNIQUE NOT NULL,
    expires_at DATETIME NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_used_at DATETIME NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Insert default admin user (password: admin123 - change this!)
INSERT INTO users (username, email, password, role) VALUES 
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin');

-- Insert default services based on your website
INSERT INTO services (name, slug, description, short_description, icon, is_active, sort_order) VALUES
('Civil Engineering', 'civil-engineering', 'Comprehensive civil engineering solutions for complex construction projects.', 'Professional civil engineering services with expertise in infrastructure development.', 'fas fa-hard-hat', TRUE, 1),
('Groundworks', 'groundworks', 'Expert groundwork services including excavation, foundations, and site preparation.', 'Complete groundwork solutions from excavation to foundation laying.', 'fas fa-tools', TRUE, 2),
('RC Frames', 'rc-frames', 'Reinforced concrete frame construction for residential and commercial buildings.', 'Structural concrete frame construction with precision and quality.', 'fas fa-building', TRUE, 3),
('Basements', 'basements', 'Basement construction and waterproofing services for residential and commercial properties.', 'Professional basement construction with waterproofing solutions.', 'fas fa-home', TRUE, 4),
('Hard Landscaping', 'hard-landscaping', 'Professional hard landscaping services including paving, driveways, and outdoor structures.', 'Transform outdoor spaces with professional hard landscaping.', 'fas fa-tree', TRUE, 5);

-- Insert default company settings
INSERT INTO settings (setting_key, setting_value, setting_type, description) VALUES
('company_name', 'Flori Construction Ltd', 'text', 'Company name'),
('company_phone', '0208 914 7883', 'text', 'Main phone number'),
('company_mobile', '078 8292 3621', 'text', 'Mobile phone number'),
('company_email', '<EMAIL>', 'text', 'Main email address'),
('company_address', '662 High Road North Finchley, London N12 0NL', 'textarea', 'Company address'),
('facebook_url', 'https://www.facebook.com/FloriConstructionLtd', 'text', 'Facebook page URL'),
('instagram_url', 'https://www.instagram.com/flori_construction_ltd/', 'text', 'Instagram page URL'),
('youtube_url', 'https://www.youtube.com/@floriconstructionltd7045', 'text', 'YouTube channel URL'),
('linkedin_url', 'https://www.linkedin.com/in/floriconstructionltd/', 'text', 'LinkedIn page URL'),
('site_title', 'Flori Construction Ltd - Professional Construction Services in London', 'text', 'Website title'),
('site_description', 'Leading construction company in London specializing in civil engineering, groundworks, RC frames, basements, and hard landscaping.', 'textarea', 'Website description');

{"name": "FloriConstructionAdmin", "version": "1.0.0", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@expo/vector-icons": "^13.0.0", "@react-navigation/bottom-tabs": "^6.5.8", "@react-navigation/native": "^6.1.7", "@react-navigation/native-stack": "^6.9.13", "@react-navigation/drawer": "^6.6.3", "expo": "~49.0.0", "expo-status-bar": "~1.6.0", "expo-image-picker": "~14.3.2", "expo-document-picker": "~11.5.4", "expo-notifications": "~0.20.1", "expo-secure-store": "~12.3.1", "expo-camera": "~13.4.2", "expo-media-library": "~15.4.1", "react": "18.2.0", "react-native": "0.72.3", "react-native-safe-area-context": "4.6.3", "react-native-screens": "~3.22.0", "react-native-gesture-handler": "~2.12.0", "react-native-reanimated": "~3.3.0", "react-native-paper": "^5.10.1", "react-native-vector-icons": "^10.0.0", "axios": "^1.5.0", "react-hook-form": "^7.45.4", "react-native-chart-kit": "^6.12.0", "react-native-svg": "13.9.0", "react-native-modal": "^13.0.1", "react-native-flash-message": "^0.4.2", "react-native-image-viewing": "^0.2.2", "react-native-super-grid": "^4.9.6", "react-native-skeleton-placeholder": "^5.2.4", "react-native-pull-to-refresh": "^2.1.3", "react-native-async-storage": "^1.19.3"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~18.2.14", "@types/react-native": "~0.72.2", "typescript": "^5.1.3"}, "private": true}
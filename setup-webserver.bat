@echo off
echo ========================================
echo Flori Construction Website Setup
echo ========================================
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% == 0 (
    echo Running with administrator privileges...
) else (
    echo ERROR: This script must be run as Administrator!
    echo Right-click and select "Run as administrator"
    pause
    exit /b 1
)

echo.
echo Setting up web server configuration...
echo.

REM Set XAMPP path (adjust if your XAMPP is installed elsewhere)
set XAMPP_PATH=C:\xampp

REM Check if XAMPP exists
if not exist "%XAMPP_PATH%" (
    echo ERROR: XAMPP not found at %XAMPP_PATH%
    echo Please install XAMPP or update the XAMPP_PATH in this script
    pause
    exit /b 1
)

echo Found XAMPP at: %XAMPP_PATH%
echo.

REM Copy virtual host configuration
echo Copying virtual host configuration...
if exist "flori-construction.conf" (
    copy "flori-construction.conf" "%XAMPP_PATH%\apache\conf\extra\"
    echo Virtual host configuration copied successfully.
) else (
    echo ERROR: flori-construction.conf not found in current directory
    pause
    exit /b 1
)

REM Backup original httpd.conf
echo Creating backup of httpd.conf...
if not exist "%XAMPP_PATH%\apache\conf\httpd.conf.backup" (
    copy "%XAMPP_PATH%\apache\conf\httpd.conf" "%XAMPP_PATH%\apache\conf\httpd.conf.backup"
    echo Backup created: httpd.conf.backup
)

REM Check if virtual host include is already present
findstr /C:"Include conf/extra/flori-construction.conf" "%XAMPP_PATH%\apache\conf\httpd.conf" >nul
if %errorLevel% == 0 (
    echo Virtual host include already present in httpd.conf
) else (
    echo Adding virtual host include to httpd.conf...
    echo. >> "%XAMPP_PATH%\apache\conf\httpd.conf"
    echo # Flori Construction Virtual Host >> "%XAMPP_PATH%\apache\conf\httpd.conf"
    echo Include conf/extra/flori-construction.conf >> "%XAMPP_PATH%\apache\conf\httpd.conf"
    echo Virtual host include added to httpd.conf
)

REM Add local domain to hosts file
echo.
echo Adding local domain to hosts file...
findstr /C:"flori-construction.local" "%WINDIR%\System32\drivers\etc\hosts" >nul
if %errorLevel% == 0 (
    echo Local domain already present in hosts file
) else (
    echo 127.0.0.1    flori-construction.local >> "%WINDIR%\System32\drivers\etc\hosts"
    echo 127.0.0.1    www.flori-construction.local >> "%WINDIR%\System32\drivers\etc\hosts"
    echo Local domain added to hosts file
)

REM Create uploads directory if it doesn't exist
if not exist "uploads" (
    mkdir uploads
    echo Created uploads directory
)

REM Set permissions for uploads directory
echo Setting permissions for uploads directory...
icacls "uploads" /grant "Everyone:(OI)(CI)F" /T >nul 2>&1

echo.
echo ========================================
echo Setup completed successfully!
echo ========================================
echo.
echo Next steps:
echo 1. Restart Apache in XAMPP Control Panel
echo 2. Import database schema from database/schema.sql
echo 3. Access your website at: http://flori-construction.local
echo 4. Access admin panel at: http://flori-construction.local/admin
echo.
echo Note: Make sure MySQL is running in XAMPP for the website to work properly.
echo.
pause

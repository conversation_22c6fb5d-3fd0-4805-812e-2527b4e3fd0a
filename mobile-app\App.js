import React, { useEffect, useState } from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { createDrawerNavigator } from '@react-navigation/drawer';
import { Provider as PaperProvider } from 'react-native-paper';
import FlashMessage from 'react-native-flash-message';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Screens
import LoginScreen from './src/screens/LoginScreen';
import DashboardScreen from './src/screens/DashboardScreen';
import ProjectsScreen from './src/screens/ProjectsScreen';
import ProjectDetailScreen from './src/screens/ProjectDetailScreen';
import AddProjectScreen from './src/screens/AddProjectScreen';
import ServicesScreen from './src/screens/ServicesScreen';
import ServiceDetailScreen from './src/screens/ServiceDetailScreen';
import AddServiceScreen from './src/screens/AddServiceScreen';
import MediaScreen from './src/screens/MediaScreen';
import MediaDetailScreen from './src/screens/MediaDetailScreen';
import UploadMediaScreen from './src/screens/UploadMediaScreen';
import MessagesScreen from './src/screens/MessagesScreen';
import MessageDetailScreen from './src/screens/MessageDetailScreen';
import ProfileScreen from './src/screens/ProfileScreen';
import SettingsScreen from './src/screens/SettingsScreen';

// Components
import CustomDrawerContent from './src/components/CustomDrawerContent';
import LoadingScreen from './src/components/LoadingScreen';

// Context
import { AuthProvider, useAuth } from './src/context/AuthContext';
import { ApiProvider } from './src/context/ApiContext';

// Theme
import { theme } from './src/theme/theme';

const Stack = createNativeStackNavigator();
const Drawer = createDrawerNavigator();

// Auth Stack (Login)
function AuthStack() {
  return (
    <Stack.Navigator screenOptions={{ headerShown: false }}>
      <Stack.Screen name="Login" component={LoginScreen} />
    </Stack.Navigator>
  );
}

// Projects Stack
function ProjectsStack() {
  return (
    <Stack.Navigator>
      <Stack.Screen 
        name="ProjectsList" 
        component={ProjectsScreen} 
        options={{ title: 'Projects' }}
      />
      <Stack.Screen 
        name="ProjectDetail" 
        component={ProjectDetailScreen} 
        options={{ title: 'Project Details' }}
      />
      <Stack.Screen 
        name="AddProject" 
        component={AddProjectScreen} 
        options={{ title: 'Add Project' }}
      />
    </Stack.Navigator>
  );
}

// Services Stack
function ServicesStack() {
  return (
    <Stack.Navigator>
      <Stack.Screen 
        name="ServicesList" 
        component={ServicesScreen} 
        options={{ title: 'Services' }}
      />
      <Stack.Screen 
        name="ServiceDetail" 
        component={ServiceDetailScreen} 
        options={{ title: 'Service Details' }}
      />
      <Stack.Screen 
        name="AddService" 
        component={AddServiceScreen} 
        options={{ title: 'Add Service' }}
      />
    </Stack.Navigator>
  );
}

// Media Stack
function MediaStack() {
  return (
    <Stack.Navigator>
      <Stack.Screen 
        name="MediaList" 
        component={MediaScreen} 
        options={{ title: 'Media Gallery' }}
      />
      <Stack.Screen 
        name="MediaDetail" 
        component={MediaDetailScreen} 
        options={{ title: 'Media Details' }}
      />
      <Stack.Screen 
        name="UploadMedia" 
        component={UploadMediaScreen} 
        options={{ title: 'Upload Media' }}
      />
    </Stack.Navigator>
  );
}

// Messages Stack
function MessagesStack() {
  return (
    <Stack.Navigator>
      <Stack.Screen 
        name="MessagesList" 
        component={MessagesScreen} 
        options={{ title: 'Messages' }}
      />
      <Stack.Screen 
        name="MessageDetail" 
        component={MessageDetailScreen} 
        options={{ title: 'Message Details' }}
      />
    </Stack.Navigator>
  );
}

// Main App Drawer
function AppDrawer() {
  return (
    <Drawer.Navigator
      drawerContent={(props) => <CustomDrawerContent {...props} />}
      screenOptions={{
        headerStyle: {
          backgroundColor: theme.colors.primary,
        },
        headerTintColor: '#fff',
        headerTitleStyle: {
          fontWeight: 'bold',
        },
        drawerStyle: {
          backgroundColor: '#f8f9fa',
        },
        drawerActiveTintColor: theme.colors.primary,
        drawerInactiveTintColor: '#666',
      }}
    >
      <Drawer.Screen 
        name="Dashboard" 
        component={DashboardScreen}
        options={{
          drawerIcon: ({ color, size }) => (
            <Ionicons name="speedometer-outline" size={size} color={color} />
          ),
        }}
      />
      <Drawer.Screen 
        name="Projects" 
        component={ProjectsStack}
        options={{
          drawerIcon: ({ color, size }) => (
            <Ionicons name="business-outline" size={size} color={color} />
          ),
        }}
      />
      <Drawer.Screen 
        name="Services" 
        component={ServicesStack}
        options={{
          drawerIcon: ({ color, size }) => (
            <Ionicons name="construct-outline" size={size} color={color} />
          ),
        }}
      />
      <Drawer.Screen 
        name="Media" 
        component={MediaStack}
        options={{
          drawerIcon: ({ color, size }) => (
            <Ionicons name="images-outline" size={size} color={color} />
          ),
        }}
      />
      <Drawer.Screen 
        name="Messages" 
        component={MessagesStack}
        options={{
          drawerIcon: ({ color, size }) => (
            <Ionicons name="mail-outline" size={size} color={color} />
          ),
        }}
      />
      <Drawer.Screen 
        name="Profile" 
        component={ProfileScreen}
        options={{
          drawerIcon: ({ color, size }) => (
            <Ionicons name="person-outline" size={size} color={color} />
          ),
        }}
      />
      <Drawer.Screen 
        name="Settings" 
        component={SettingsScreen}
        options={{
          drawerIcon: ({ color, size }) => (
            <Ionicons name="settings-outline" size={size} color={color} />
          ),
        }}
      />
    </Drawer.Navigator>
  );
}

// Main App Component
function AppContent() {
  const { isAuthenticated, isLoading } = useAuth();

  if (isLoading) {
    return <LoadingScreen />;
  }

  return (
    <NavigationContainer>
      {isAuthenticated ? <AppDrawer /> : <AuthStack />}
    </NavigationContainer>
  );
}

// Root App Component
export default function App() {
  return (
    <PaperProvider theme={theme}>
      <AuthProvider>
        <ApiProvider>
          <AppContent />
          <FlashMessage position="top" />
        </ApiProvider>
      </AuthProvider>
    </PaperProvider>
  );
}

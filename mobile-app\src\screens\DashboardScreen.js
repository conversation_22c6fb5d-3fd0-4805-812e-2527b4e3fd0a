import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  RefreshControl,
  Dimensions,
} from 'react-native';
import {
  Card,
  Title,
  Paragraph,
  Button,
  ActivityIndicator,
  Surface,
  Text,
  IconButton,
} from 'react-native-paper';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'react-native-chart-kit';
import { useAuth } from '../context/AuthContext';
import { apiService } from '../services/apiService';
import { theme } from '../theme/theme';
import { showMessage } from 'react-native-flash-message';

const screenWidth = Dimensions.get('window').width;

export default function DashboardScreen({ navigation }) {
  const [stats, setStats] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const { user, token } = useAuth();

  const loadDashboardData = useCallback(async (isRefresh = false) => {
    try {
      if (isRefresh) {
        setIsRefreshing(true);
      } else {
        setIsLoading(true);
      }

      const dashboardStats = await apiService.getDashboardStats(token);
      setStats(dashboardStats);
    } catch (error) {
      console.error('Error loading dashboard data:', error);
      showMessage({
        message: 'Error',
        description: 'Failed to load dashboard data',
        type: 'danger',
      });
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  }, [token]);

  useEffect(() => {
    loadDashboardData();
  }, [loadDashboardData]);

  const onRefresh = () => {
    loadDashboardData(true);
  };

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={theme.colors.primary} />
        <Text style={styles.loadingText}>Loading dashboard...</Text>
      </View>
    );
  }

  const chartConfig = {
    backgroundColor: theme.colors.surface,
    backgroundGradientFrom: theme.colors.surface,
    backgroundGradientTo: theme.colors.surface,
    decimalPlaces: 0,
    color: (opacity = 1) => `rgba(255, 107, 53, ${opacity})`,
    labelColor: (opacity = 1) => `rgba(0, 0, 0, ${opacity})`,
    style: {
      borderRadius: 16,
    },
    propsForDots: {
      r: '6',
      strokeWidth: '2',
      stroke: theme.colors.primary,
    },
  };

  const projectsData = stats?.projects ? [
    {
      name: 'Completed',
      population: stats.projects.completed_projects,
      color: '#4CAF50',
      legendFontColor: '#7F7F7F',
      legendFontSize: 15,
    },
    {
      name: 'Ongoing',
      population: stats.projects.ongoing_projects,
      color: '#FF9800',
      legendFontColor: '#7F7F7F',
      legendFontSize: 15,
    },
  ] : [];

  return (
    <ScrollView
      style={styles.container}
      refreshControl={
        <RefreshControl refreshing={isRefreshing} onRefresh={onRefresh} />
      }
    >
      {/* Welcome Section */}
      <Surface style={styles.welcomeCard}>
        <View style={styles.welcomeContent}>
          <View>
            <Title style={styles.welcomeTitle}>
              Welcome back, {user?.username}!
            </Title>
            <Paragraph style={styles.welcomeSubtitle}>
              Here's what's happening with your projects
            </Paragraph>
          </View>
          <IconButton
            icon="bell-outline"
            size={24}
            onPress={() => navigation.navigate('Messages')}
          />
        </View>
      </Surface>

      {/* Stats Cards */}
      <View style={styles.statsContainer}>
        <View style={styles.statsRow}>
          <Card style={[styles.statCard, { backgroundColor: '#E3F2FD' }]}>
            <Card.Content style={styles.statContent}>
              <View style={styles.statHeader}>
                <IconButton icon="building" size={24} iconColor="#1976D2" />
                <Text style={[styles.statNumber, { color: '#1976D2' }]}>
                  {stats?.projects?.total_projects || 0}
                </Text>
              </View>
              <Text style={styles.statLabel}>Total Projects</Text>
            </Card.Content>
          </Card>

          <Card style={[styles.statCard, { backgroundColor: '#E8F5E8' }]}>
            <Card.Content style={styles.statContent}>
              <View style={styles.statHeader}>
                <IconButton icon="check-circle" size={24} iconColor="#388E3C" />
                <Text style={[styles.statNumber, { color: '#388E3C' }]}>
                  {stats?.projects?.completed_projects || 0}
                </Text>
              </View>
              <Text style={styles.statLabel}>Completed</Text>
            </Card.Content>
          </Card>
        </View>

        <View style={styles.statsRow}>
          <Card style={[styles.statCard, { backgroundColor: '#FFF3E0' }]}>
            <Card.Content style={styles.statContent}>
              <View style={styles.statHeader}>
                <IconButton icon="tools" size={24} iconColor="#F57C00" />
                <Text style={[styles.statNumber, { color: '#F57C00' }]}>
                  {stats?.services?.active_services || 0}
                </Text>
              </View>
              <Text style={styles.statLabel}>Active Services</Text>
            </Card.Content>
          </Card>

          <Card style={[styles.statCard, { backgroundColor: '#FFEBEE' }]}>
            <Card.Content style={styles.statContent}>
              <View style={styles.statHeader}>
                <IconButton icon="email" size={24} iconColor="#D32F2F" />
                <Text style={[styles.statNumber, { color: '#D32F2F' }]}>
                  {stats?.messages?.new_messages || 0}
                </Text>
              </View>
              <Text style={styles.statLabel}>New Messages</Text>
            </Card.Content>
          </Card>
        </View>
      </View>

      {/* Charts Section */}
      {projectsData.length > 0 && (
        <Card style={styles.chartCard}>
          <Card.Content>
            <Title style={styles.chartTitle}>Projects Overview</Title>
            <PieChart
              data={projectsData}
              width={screenWidth - 80}
              height={220}
              chartConfig={chartConfig}
              accessor="population"
              backgroundColor="transparent"
              paddingLeft="15"
              center={[10, 50]}
              absolute
            />
          </Card.Content>
        </Card>
      )}

      {/* Quick Actions */}
      <Card style={styles.actionsCard}>
        <Card.Content>
          <Title style={styles.actionsTitle}>Quick Actions</Title>
          <View style={styles.actionsContainer}>
            <Button
              mode="contained"
              icon="plus"
              style={styles.actionButton}
              onPress={() => navigation.navigate('Projects', { screen: 'AddProject' })}
            >
              Add Project
            </Button>
            <Button
              mode="outlined"
              icon="upload"
              style={styles.actionButton}
              onPress={() => navigation.navigate('Media', { screen: 'UploadMedia' })}
            >
              Upload Media
            </Button>
          </View>
          <View style={styles.actionsContainer}>
            <Button
              mode="outlined"
              icon="cog"
              style={styles.actionButton}
              onPress={() => navigation.navigate('Services', { screen: 'AddService' })}
            >
              Add Service
            </Button>
            <Button
              mode="outlined"
              icon="email"
              style={styles.actionButton}
              onPress={() => navigation.navigate('Messages')}
            >
              View Messages
            </Button>
          </View>
        </Card.Content>
      </Card>

      {/* Recent Activity */}
      <Card style={styles.activityCard}>
        <Card.Content>
          <Title style={styles.activityTitle}>Recent Activity</Title>
          {stats?.projects?.recent_projects?.length > 0 ? (
            stats.projects.recent_projects.slice(0, 3).map((project, index) => (
              <View key={project.id} style={styles.activityItem}>
                <View style={styles.activityIcon}>
                  <IconButton icon="building" size={20} />
                </View>
                <View style={styles.activityContent}>
                  <Text style={styles.activityTitle}>{project.title}</Text>
                  <Text style={styles.activitySubtitle}>
                    {project.category} • {new Date(project.created_at).toLocaleDateString()}
                  </Text>
                </View>
              </View>
            ))
          ) : (
            <Paragraph>No recent activity</Paragraph>
          )}
        </Card.Content>
      </Card>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: theme.colors.background,
  },
  loadingText: {
    marginTop: 16,
    color: theme.colors.onSurfaceVariant,
  },
  welcomeCard: {
    margin: 16,
    padding: 16,
    borderRadius: 12,
    elevation: 2,
  },
  welcomeContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  welcomeTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: theme.colors.onSurface,
  },
  welcomeSubtitle: {
    color: theme.colors.onSurfaceVariant,
    marginTop: 4,
  },
  statsContainer: {
    paddingHorizontal: 16,
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  statCard: {
    flex: 1,
    marginHorizontal: 4,
    borderRadius: 12,
    elevation: 2,
  },
  statContent: {
    alignItems: 'center',
    paddingVertical: 8,
  },
  statHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  statLabel: {
    fontSize: 12,
    color: theme.colors.onSurfaceVariant,
    textAlign: 'center',
    marginTop: 4,
  },
  chartCard: {
    margin: 16,
    borderRadius: 12,
    elevation: 2,
  },
  chartTitle: {
    textAlign: 'center',
    marginBottom: 16,
    color: theme.colors.onSurface,
  },
  actionsCard: {
    margin: 16,
    borderRadius: 12,
    elevation: 2,
  },
  actionsTitle: {
    marginBottom: 16,
    color: theme.colors.onSurface,
  },
  actionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  actionButton: {
    flex: 1,
    marginHorizontal: 4,
  },
  activityCard: {
    margin: 16,
    borderRadius: 12,
    elevation: 2,
    marginBottom: 32,
  },
  activityTitle: {
    marginBottom: 16,
    color: theme.colors.onSurface,
  },
  activityItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.outline,
  },
  activityIcon: {
    marginRight: 12,
  },
  activityContent: {
    flex: 1,
  },
  activityTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.colors.onSurface,
  },
  activitySubtitle: {
    fontSize: 12,
    color: theme.colors.onSurfaceVariant,
    marginTop: 2,
  },
});

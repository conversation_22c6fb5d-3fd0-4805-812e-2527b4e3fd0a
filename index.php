<?php
$pageTitle = 'Home';
$pageDescription = 'Professional construction services in London. Specializing in civil engineering, groundworks, RC frames, basements, and hard landscaping.';

require_once 'includes/header.php';

// Get featured projects
$featuredProjects = getFeaturedProjects(6);

// Get active services
$services = getActiveServices();

// Get recent media
$recentMedia = getRecentMedia(8);
?>

<!-- Hero Section -->
<section class="hero-section position-relative overflow-hidden">
    <div class="hero-bg">
        <img src="/assets/images/hero-bg.jpg" alt="Construction Site" class="w-100 h-100 object-fit-cover">
        <div class="hero-overlay position-absolute top-0 start-0 w-100 h-100"></div>
    </div>
    
    <div class="hero-content position-absolute top-50 start-50 translate-middle text-center text-white w-100">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <h1 class="display-4 fw-bold mb-4 animate-fade-up">
                        Building Excellence, <span class="text-primary">Delivering Dreams</span>
                    </h1>
                    <p class="lead mb-4 animate-fade-up" style="animation-delay: 0.2s;">
                        Professional construction services in London with over a decade of experience in civil engineering, groundworks, and specialized construction projects.
                    </p>
                    <div class="hero-buttons animate-fade-up" style="animation-delay: 0.4s;">
                        <a href="/projects.php" class="btn btn-primary btn-lg me-3">
                            <i class="fas fa-eye me-2"></i>View Our Work
                        </a>
                        <a href="/contact.php" class="btn btn-outline-light btn-lg">
                            <i class="fas fa-phone me-2"></i>Get Free Quote
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Scroll Indicator -->
    <div class="scroll-indicator position-absolute bottom-0 start-50 translate-middle-x mb-4">
        <div class="scroll-arrow animate-bounce">
            <i class="fas fa-chevron-down text-white"></i>
        </div>
    </div>
</section>

<!-- Stats Section -->
<section class="stats-section py-5 bg-primary text-white">
    <div class="container">
        <div class="row text-center">
            <div class="col-lg-3 col-md-6 mb-4 mb-lg-0">
                <div class="stat-item">
                    <div class="stat-icon mb-3">
                        <i class="fas fa-hard-hat fa-3x"></i>
                    </div>
                    <h3 class="stat-number fw-bold" data-count="150">0</h3>
                    <p class="stat-label">Projects Completed</p>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4 mb-lg-0">
                <div class="stat-item">
                    <div class="stat-icon mb-3">
                        <i class="fas fa-users fa-3x"></i>
                    </div>
                    <h3 class="stat-number fw-bold" data-count="50">0</h3>
                    <p class="stat-label">Happy Clients</p>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4 mb-lg-0">
                <div class="stat-item">
                    <div class="stat-icon mb-3">
                        <i class="fas fa-calendar fa-3x"></i>
                    </div>
                    <h3 class="stat-number fw-bold" data-count="10">0</h3>
                    <p class="stat-label">Years Experience</p>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stat-item">
                    <div class="stat-icon mb-3">
                        <i class="fas fa-award fa-3x"></i>
                    </div>
                    <h3 class="stat-number fw-bold" data-count="25">0</h3>
                    <p class="stat-label">Awards Won</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- About Section -->
<section class="about-section py-5">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6 mb-4 mb-lg-0">
                <div class="about-content">
                    <h2 class="section-title mb-4">About Our Company</h2>
                    <p class="lead text-muted mb-4">
                        Flori Construction Ltd began its journey in the heart of London, founded on the principles of quality, professionalism, and an unwavering dedication to customer satisfaction.
                    </p>
                    <p class="mb-4">
                        From our humble beginnings, we have grown exponentially, establishing ourselves as a premier construction company renowned for our exceptional services in Civil Engineering, Professional Groundworks, RC Frames Construction, Basement Construction, and Hard Landscaping.
                    </p>
                    
                    <div class="about-features">
                        <div class="feature-item d-flex align-items-center mb-3">
                            <div class="feature-icon me-3">
                                <i class="fas fa-check-circle text-primary fa-lg"></i>
                            </div>
                            <span>Professional and experienced team</span>
                        </div>
                        <div class="feature-item d-flex align-items-center mb-3">
                            <div class="feature-icon me-3">
                                <i class="fas fa-check-circle text-primary fa-lg"></i>
                            </div>
                            <span>Quality materials and workmanship</span>
                        </div>
                        <div class="feature-item d-flex align-items-center mb-3">
                            <div class="feature-icon me-3">
                                <i class="fas fa-check-circle text-primary fa-lg"></i>
                            </div>
                            <span>On-time project delivery</span>
                        </div>
                        <div class="feature-item d-flex align-items-center mb-4">
                            <div class="feature-icon me-3">
                                <i class="fas fa-check-circle text-primary fa-lg"></i>
                            </div>
                            <span>Competitive pricing</span>
                        </div>
                    </div>
                    
                    <a href="/about.php" class="btn btn-primary">
                        <i class="fas fa-arrow-right me-2"></i>Learn More About Us
                    </a>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="about-image">
                    <img src="/assets/images/about-us.jpg" alt="About Flori Construction" class="img-fluid rounded shadow">
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Services Section -->
<section class="services-section py-5 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center mb-5">
                <h2 class="section-title mb-4">Our Services</h2>
                <p class="lead text-muted">
                    We provide comprehensive construction services with expertise in various specialized areas. Our commitment to quality and professionalism sets us apart.
                </p>
            </div>
        </div>
        
        <div class="row">
            <?php foreach ($services as $service): ?>
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="service-card h-100 bg-white rounded shadow-sm p-4 text-center">
                    <div class="service-icon mb-3">
                        <i class="<?php echo $service['icon']; ?> fa-3x text-primary"></i>
                    </div>
                    <h4 class="service-title mb-3"><?php echo $service['name']; ?></h4>
                    <p class="service-description text-muted mb-4">
                        <?php echo $service['short_description']; ?>
                    </p>
                    <a href="/services.php#<?php echo $service['slug']; ?>" class="btn btn-outline-primary">
                        Learn More <i class="fas fa-arrow-right ms-1"></i>
                    </a>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
        
        <div class="text-center mt-4">
            <a href="/services.php" class="btn btn-primary btn-lg">
                <i class="fas fa-list me-2"></i>View All Services
            </a>
        </div>
    </div>
</section>

<!-- Featured Projects Section -->
<?php if (!empty($featuredProjects)): ?>
<section class="projects-section py-5">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center mb-5">
                <h2 class="section-title mb-4">Featured Projects</h2>
                <p class="lead text-muted">
                    Take a look at some of our recent completed projects that showcase our expertise and commitment to excellence.
                </p>
            </div>
        </div>
        
        <div class="row">
            <?php foreach (array_slice($featuredProjects, 0, 6) as $project): ?>
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="project-card">
                    <div class="project-image">
                        <img src="/<?php echo $project['image_main'] ?: 'assets/images/placeholder-project.jpg'; ?>" 
                             alt="<?php echo $project['title']; ?>" 
                             class="img-fluid rounded">
                        <div class="project-overlay">
                            <div class="project-info text-white text-center">
                                <h5 class="project-title"><?php echo $project['title']; ?></h5>
                                <p class="project-type"><?php echo $project['project_type']; ?></p>
                                <a href="/projects.php?id=<?php echo $project['id']; ?>" class="btn btn-light btn-sm">
                                    View Details
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
        
        <div class="text-center mt-4">
            <a href="/projects.php" class="btn btn-primary btn-lg">
                <i class="fas fa-eye me-2"></i>View All Projects
            </a>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- CTA Section -->
<section class="cta-section py-5 bg-primary text-white">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h3 class="mb-3">Ready to Start Your Construction Project?</h3>
                <p class="lead mb-0">
                    Get in touch with our expert team for a free consultation and quote. We're here to bring your vision to life.
                </p>
            </div>
            <div class="col-lg-4 text-lg-end">
                <a href="/contact.php" class="btn btn-light btn-lg">
                    <i class="fas fa-phone me-2"></i>Get Free Quote
                </a>
            </div>
        </div>
    </div>
</section>

<?php
$additionalScripts = '
<script>
// Counter animation
function animateCounters() {
    $(".stat-number").each(function() {
        const $this = $(this);
        const countTo = $this.attr("data-count");
        
        $({ countNum: $this.text() }).animate({
            countNum: countTo
        }, {
            duration: 2000,
            easing: "linear",
            step: function() {
                $this.text(Math.floor(this.countNum));
            },
            complete: function() {
                $this.text(this.countNum);
            }
        });
    });
}

// Trigger counter animation when stats section is in view
$(window).on("scroll", function() {
    const statsSection = $(".stats-section");
    const scrollTop = $(window).scrollTop();
    const elementTop = statsSection.offset().top;
    const elementHeight = statsSection.height();
    const windowHeight = $(window).height();
    
    if (scrollTop > elementTop - windowHeight && scrollTop < elementTop + elementHeight) {
        if (!statsSection.hasClass("animated")) {
            statsSection.addClass("animated");
            animateCounters();
        }
    }
});
</script>
';

require_once 'includes/footer.php';
?>

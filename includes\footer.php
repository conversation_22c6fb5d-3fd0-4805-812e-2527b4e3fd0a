    <!-- Footer -->
    <footer class="bg-dark text-white py-5 mt-5">
        <div class="container">
            <div class="row">
                <!-- Company Info -->
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="footer-section">
                        <img src="/assets/images/logo-white.png" alt="<?php echo getSetting('company_name'); ?>" height="40" class="mb-3">
                        <p class="text-light">
                            <?php echo getSetting('site_description'); ?>
                        </p>
                        <div class="social-links mt-3">
                            <?php if (getSetting('facebook_url')): ?>
                            <a href="<?php echo getSetting('facebook_url'); ?>" target="_blank" class="text-white me-3">
                                <i class="fab fa-facebook-f fa-lg"></i>
                            </a>
                            <?php endif; ?>
                            
                            <?php if (getSetting('instagram_url')): ?>
                            <a href="<?php echo getSetting('instagram_url'); ?>" target="_blank" class="text-white me-3">
                                <i class="fab fa-instagram fa-lg"></i>
                            </a>
                            <?php endif; ?>
                            
                            <?php if (getSetting('youtube_url')): ?>
                            <a href="<?php echo getSetting('youtube_url'); ?>" target="_blank" class="text-white me-3">
                                <i class="fab fa-youtube fa-lg"></i>
                            </a>
                            <?php endif; ?>
                            
                            <?php if (getSetting('linkedin_url')): ?>
                            <a href="<?php echo getSetting('linkedin_url'); ?>" target="_blank" class="text-white">
                                <i class="fab fa-linkedin-in fa-lg"></i>
                            </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Quick Links -->
                <div class="col-lg-2 col-md-6 mb-4">
                    <div class="footer-section">
                        <h5 class="text-white mb-3">Quick Links</h5>
                        <ul class="list-unstyled">
                            <li class="mb-2">
                                <a href="/" class="text-light text-decoration-none">
                                    <i class="fas fa-chevron-right me-2 small"></i>Home
                                </a>
                            </li>
                            <li class="mb-2">
                                <a href="/about.php" class="text-light text-decoration-none">
                                    <i class="fas fa-chevron-right me-2 small"></i>About Us
                                </a>
                            </li>
                            <li class="mb-2">
                                <a href="/services.php" class="text-light text-decoration-none">
                                    <i class="fas fa-chevron-right me-2 small"></i>Our Services
                                </a>
                            </li>
                            <li class="mb-2">
                                <a href="/projects.php" class="text-light text-decoration-none">
                                    <i class="fas fa-chevron-right me-2 small"></i>Our Projects
                                </a>
                            </li>
                            <li class="mb-2">
                                <a href="/media.php" class="text-light text-decoration-none">
                                    <i class="fas fa-chevron-right me-2 small"></i>Media
                                </a>
                            </li>
                            <li class="mb-2">
                                <a href="/contact.php" class="text-light text-decoration-none">
                                    <i class="fas fa-chevron-right me-2 small"></i>Contact Us
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>

                <!-- Services -->
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="footer-section">
                        <h5 class="text-white mb-3">Our Services</h5>
                        <ul class="list-unstyled">
                            <?php
                            $services = getActiveServices();
                            foreach ($services as $service):
                            ?>
                            <li class="mb-2">
                                <a href="/services.php#<?php echo $service['slug']; ?>" class="text-light text-decoration-none">
                                    <i class="fas fa-chevron-right me-2 small"></i><?php echo $service['name']; ?>
                                </a>
                            </li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                </div>

                <!-- Contact Info -->
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="footer-section">
                        <h5 class="text-white mb-3">Contact Info</h5>
                        <div class="contact-info">
                            <div class="mb-3">
                                <i class="fas fa-map-marker-alt me-2"></i>
                                <span class="text-light"><?php echo getSetting('company_address'); ?></span>
                            </div>
                            <div class="mb-3">
                                <i class="fas fa-phone me-2"></i>
                                <a href="tel:<?php echo getSetting('company_phone'); ?>" class="text-light text-decoration-none">
                                    <?php echo getSetting('company_phone'); ?>
                                </a>
                            </div>
                            <?php if (getSetting('company_mobile')): ?>
                            <div class="mb-3">
                                <i class="fas fa-mobile-alt me-2"></i>
                                <a href="tel:<?php echo getSetting('company_mobile'); ?>" class="text-light text-decoration-none">
                                    <?php echo getSetting('company_mobile'); ?>
                                </a>
                            </div>
                            <?php endif; ?>
                            <div class="mb-3">
                                <i class="fas fa-envelope me-2"></i>
                                <a href="mailto:<?php echo getSetting('company_email'); ?>" class="text-light text-decoration-none">
                                    <?php echo getSetting('company_email'); ?>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Bottom Bar -->
            <hr class="my-4 border-secondary">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="mb-0 text-light">
                        &copy; <?php echo date('Y'); ?> <?php echo getSetting('company_name'); ?>. All rights reserved.
                    </p>
                </div>
                <div class="col-md-6 text-md-end">
                    <div class="footer-links">
                        <a href="/privacy-policy.php" class="text-light text-decoration-none me-3">Privacy Policy</a>
                        <a href="/terms-of-service.php" class="text-light text-decoration-none">Terms of Service</a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Back to Top Button -->
    <button id="backToTop" class="btn btn-primary position-fixed bottom-0 end-0 m-4 rounded-circle" style="display: none; z-index: 1000;">
        <i class="fas fa-chevron-up"></i>
    </button>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    
    <!-- Custom JS -->
    <script src="/assets/js/main.js"></script>
    
    <!-- Additional page-specific scripts -->
    <?php if (isset($additionalScripts)): ?>
        <?php echo $additionalScripts; ?>
    <?php endif; ?>
</body>
</html>

<?php
/**
 * Common functions for Flori Construction Website
 */

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

/**
 * Check if user is logged in (admin)
 */
function isLoggedIn() {
    return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
}

/**
 * Get current user data
 */
function getCurrentUser() {
    if (!isLoggedIn()) {
        return null;
    }
    
    return fetchOne("SELECT * FROM users WHERE id = ?", [$_SESSION['user_id']]);
}

/**
 * Require admin login
 */
function requireLogin() {
    if (!isLoggedIn()) {
        header('Location: /admin/login.php');
        exit;
    }
}

/**
 * Login user
 */
function loginUser($username, $password) {
    $user = fetchOne("SELECT * FROM users WHERE username = ? AND is_active = 1", [$username]);
    
    if ($user && password_verify($password, $user['password'])) {
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['username'] = $user['username'];
        $_SESSION['role'] = $user['role'];
        
        // Update last login
        updateData('users', ['last_login' => date('Y-m-d H:i:s')], 'id = ?', [$user['id']]);
        
        return true;
    }
    
    return false;
}

/**
 * Logout user
 */
function logoutUser() {
    session_destroy();
    header('Location: /admin/login.php');
    exit;
}

/**
 * Generate API token for mobile app
 */
function generateApiToken($userId) {
    $token = generateToken(64);
    $expiresAt = date('Y-m-d H:i:s', strtotime('+30 days'));
    
    // Remove old tokens for this user
    deleteData('api_tokens', 'user_id = ?', [$userId]);
    
    // Insert new token
    insertData('api_tokens', [
        'user_id' => $userId,
        'token' => $token,
        'expires_at' => $expiresAt
    ]);
    
    return $token;
}

/**
 * Validate API token
 */
function validateApiToken($token) {
    if (empty($token)) {
        return false;
    }
    
    // Remove 'Bearer ' prefix if present
    $token = str_replace('Bearer ', '', $token);
    
    $tokenData = fetchOne(
        "SELECT t.*, u.* FROM api_tokens t 
         JOIN users u ON t.user_id = u.id 
         WHERE t.token = ? AND t.expires_at > NOW() AND u.is_active = 1",
        [$token]
    );
    
    if ($tokenData) {
        // Update last used timestamp
        updateData('api_tokens', ['last_used_at' => date('Y-m-d H:i:s')], 'token = ?', [$token]);
        return $tokenData;
    }
    
    return false;
}

/**
 * Upload file with validation
 */
function uploadFile($file, $uploadDir = 'uploads/', $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp']) {
    if (!isset($file['tmp_name']) || empty($file['tmp_name'])) {
        throw new Exception('No file uploaded');
    }
    
    // Validate file type
    $finfo = finfo_open(FILEINFO_MIME_TYPE);
    $mimeType = finfo_file($finfo, $file['tmp_name']);
    finfo_close($finfo);
    
    if (!in_array($mimeType, $allowedTypes)) {
        throw new Exception('Invalid file type');
    }
    
    // Validate file size (max 10MB)
    if ($file['size'] > 10 * 1024 * 1024) {
        throw new Exception('File too large (max 10MB)');
    }
    
    // Generate unique filename
    $extension = getFileExtension($file['name']);
    $filename = uniqid() . '_' . time() . '.' . $extension;
    $uploadPath = $uploadDir . $filename;
    
    // Create upload directory if it doesn't exist
    if (!is_dir($uploadDir)) {
        mkdir($uploadDir, 0755, true);
    }
    
    // Move uploaded file
    if (!move_uploaded_file($file['tmp_name'], $uploadPath)) {
        throw new Exception('Failed to upload file');
    }
    
    return $uploadPath;
}

/**
 * Create thumbnail for image
 */
function createThumbnail($imagePath, $thumbWidth = 300, $thumbHeight = 200) {
    if (!isImageFile($imagePath) || !file_exists($imagePath)) {
        return false;
    }
    
    $thumbDir = dirname($imagePath) . '/thumbs/';
    if (!is_dir($thumbDir)) {
        mkdir($thumbDir, 0755, true);
    }
    
    $thumbPath = getThumbnailPath($imagePath);
    
    // Get image info
    $imageInfo = getimagesize($imagePath);
    $originalWidth = $imageInfo[0];
    $originalHeight = $imageInfo[1];
    $imageType = $imageInfo[2];
    
    // Calculate new dimensions
    $ratio = min($thumbWidth / $originalWidth, $thumbHeight / $originalHeight);
    $newWidth = intval($originalWidth * $ratio);
    $newHeight = intval($originalHeight * $ratio);
    
    // Create image resource
    switch ($imageType) {
        case IMAGETYPE_JPEG:
            $source = imagecreatefromjpeg($imagePath);
            break;
        case IMAGETYPE_PNG:
            $source = imagecreatefrompng($imagePath);
            break;
        case IMAGETYPE_GIF:
            $source = imagecreatefromgif($imagePath);
            break;
        default:
            return false;
    }
    
    // Create thumbnail
    $thumb = imagecreatetruecolor($newWidth, $newHeight);
    
    // Preserve transparency for PNG and GIF
    if ($imageType == IMAGETYPE_PNG || $imageType == IMAGETYPE_GIF) {
        imagealphablending($thumb, false);
        imagesavealpha($thumb, true);
        $transparent = imagecolorallocatealpha($thumb, 255, 255, 255, 127);
        imagefilledrectangle($thumb, 0, 0, $newWidth, $newHeight, $transparent);
    }
    
    imagecopyresampled($thumb, $source, 0, 0, 0, 0, $newWidth, $newHeight, $originalWidth, $originalHeight);
    
    // Save thumbnail
    switch ($imageType) {
        case IMAGETYPE_JPEG:
            imagejpeg($thumb, $thumbPath, 85);
            break;
        case IMAGETYPE_PNG:
            imagepng($thumb, $thumbPath);
            break;
        case IMAGETYPE_GIF:
            imagegif($thumb, $thumbPath);
            break;
    }
    
    imagedestroy($source);
    imagedestroy($thumb);
    
    return $thumbPath;
}

/**
 * Send JSON response for API
 */
function sendJsonResponse($data, $statusCode = 200) {
    http_response_code($statusCode);
    header('Content-Type: application/json');
    echo json_encode($data);
    exit;
}

/**
 * Send error response
 */
function sendErrorResponse($message, $statusCode = 400) {
    sendJsonResponse(['error' => $message], $statusCode);
}

/**
 * Send success response
 */
function sendSuccessResponse($data = [], $message = 'Success') {
    sendJsonResponse(['success' => true, 'message' => $message, 'data' => $data]);
}

/**
 * Paginate results
 */
function paginate($sql, $params = [], $page = 1, $perPage = 10) {
    $offset = ($page - 1) * $perPage;
    
    // Get total count
    $countSql = "SELECT COUNT(*) as total FROM ({$sql}) as count_table";
    $totalResult = fetchOne($countSql, $params);
    $total = $totalResult['total'];
    
    // Get paginated results
    $paginatedSql = $sql . " LIMIT {$perPage} OFFSET {$offset}";
    $results = fetchAll($paginatedSql, $params);
    
    return [
        'data' => $results,
        'pagination' => [
            'current_page' => $page,
            'per_page' => $perPage,
            'total' => $total,
            'total_pages' => ceil($total / $perPage),
            'has_next' => $page < ceil($total / $perPage),
            'has_prev' => $page > 1
        ]
    ];
}

/**
 * Get featured projects
 */
function getFeaturedProjects($limit = 6) {
    return fetchAll(
        "SELECT * FROM projects WHERE featured = 1 ORDER BY created_at DESC LIMIT ?",
        [$limit]
    );
}

/**
 * Get active services
 */
function getActiveServices() {
    return fetchAll(
        "SELECT * FROM services WHERE is_active = 1 ORDER BY sort_order ASC, name ASC"
    );
}

/**
 * Get recent media
 */
function getRecentMedia($limit = 12) {
    return fetchAll(
        "SELECT * FROM media ORDER BY created_at DESC LIMIT ?",
        [$limit]
    );
}

/**
 * Get unread messages count
 */
function getUnreadMessagesCount() {
    $result = fetchOne("SELECT COUNT(*) as count FROM messages WHERE status = 'new'");
    return $result['count'];
}

/**
 * Truncate text
 */
function truncateText($text, $length = 100, $suffix = '...') {
    if (strlen($text) <= $length) {
        return $text;
    }
    
    return substr($text, 0, $length) . $suffix;
}

/**
 * Format file size
 */
function formatFileSize($bytes) {
    $units = ['B', 'KB', 'MB', 'GB'];
    $bytes = max($bytes, 0);
    $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
    $pow = min($pow, count($units) - 1);
    
    $bytes /= pow(1024, $pow);
    
    return round($bytes, 2) . ' ' . $units[$pow];
}
?>
